name: ❓ Discussion & Question [English]
description: Seeking help, discussing problems, asking questions, etc.
title: '[Discussion]: '
labels: ['question']
body:
  - type: markdown
    attributes:
      value: |
        Thank you for your question! Please describe your problem as detailed as possible so we can help you better.

  - type: checkboxes
    id: checklist
    attributes:
      label: Issue Checklist
      description: |
        Please ensure that you have completed all of the following steps before submitting an issue
      options:
        - label: I understand that Issues are used to provide feedback and solve problems, not to complain in the comments section, and will provide more information to help solve the problem.
          required: true
        - label: I confirm that I need to raise questions and discuss problems, not Bug feedback or demand suggestions.
          required: true
        - label: I have read [Github Issues](https://github.com/yeongpin/cursor-free-vip/issues) and searched for existing [open issues](https://github.com/yeongpin/cursor-free-vip/issues) and [closed issues](https://github.com/yeongpin/cursor-free-vip/issues?q=is%3Aissue%20state%3Aclosed%20), and found no similar issues.
          required: true
          
  - type: dropdown
    id: platform
    attributes:
      label: Platform
      description: Which platform are you using?
      options:
        - Windows x32
        - Windows x64
        - macOS Intel
        - macOS ARM64
        - Linux x64
        - Linux ARM64
    validations:
      required: true

  - type: input
    id: version
    attributes:
      label: Version
      description: What version of Cursor Free Vip are you running?
      placeholder: For example v1.0.0
    validations:
      required: true

  - type: textarea
    id: question
    attributes:
      label: Your question
      description: Please describe your problem as detailed as possible
      placeholder: Please explain your question as clearly as possible...
    validations:
      required: true

  - type: textarea
    id: additional
    attributes:
      label: Additional information
      description: Any other related information, screenshots, or code examples
      render: shell

  - type: dropdown
    id: priority
    attributes:
      label: Priority
      description: How urgent is this issue for you?
      options:
        - Low (I'll look at it when I have time)
        - Medium (I hope to get an answer soon)
        - High (It blocks my work)
    validations:
      required: true