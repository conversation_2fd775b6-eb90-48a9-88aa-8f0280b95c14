{"name": "auto-cursor", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@tauri-apps/api": "^2", "@tauri-apps/plugin-opener": "^2", "@types/react-router-dom": "^5.3.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.8.2"}, "devDependencies": {"@tauri-apps/cli": "^2", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.19", "postcss": "^8.4.39", "tailwindcss": "^3.4.6", "typescript": "~5.8.3", "vite": "^7.0.4"}}