# 邮箱显示功能使用说明

## 功能概述

这个功能允许您在切换Cursor账户时自动更新设置页面中显示的邮箱地址。

## 自动调用

### 1. 账户切换时自动更新
当您使用程序的账户切换功能时，邮箱显示会自动更新：

```typescript
// 通过前端界面切换账户
await AccountService.switchAccount('<EMAIL>');
// 邮箱显示会自动更新为 '<EMAIL>'
```

## 手动调用

### 1. 通过前端API
```typescript
import { CursorService } from './services/cursorService';

// 设置当前显示的邮箱
await CursorService.setCurrentEmail('<EMAIL>');

// 获取当前存储的邮箱
const currentEmail = await CursorService.getCurrentEmail();
console.log('Current email:', currentEmail);
```

### 2. 通过浏览器控制台
在Cursor设置页面中：

```javascript
// 更新邮箱显示
window.cursorUpdateEmail('<EMAIL>');

// 检查是否成功
console.log('Email updated!');
```

### 3. 通过Rust后端
```rust
use crate::machine_id::MachineIdRestorer;

let restorer = MachineIdRestorer::new()?;

// 设置邮箱
restorer.set_current_email("<EMAIL>")?;

// 获取邮箱
let current_email = restorer.get_current_email()?;
println!("Current email: {:?}", current_email);
```

## 工作原理

1. **JavaScript注入**: 程序会在`workbench.desktop.main.js`中注入JavaScript代码
2. **DOM监听**: 注入的代码会监听DOM变化，自动查找邮箱显示元素
3. **持久化存储**: 邮箱信息会保存在localStorage和配置文件中
4. **自动恢复**: 每次打开设置页面时会自动恢复显示的邮箱

## 注意事项

- 需要先运行一次程序来注入JavaScript代码
- 邮箱修改只影响显示，不会改变实际的账户登录状态
- 如果Cursor更新，可能需要重新注入代码
- 目标元素选择器: `p[class="cursor-settings-sidebar-header-email"]`

## 故障排除

### 1. 邮箱显示没有更新
- 检查Cursor设置页面是否已打开
- 在浏览器控制台查看是否有错误信息
- 确认JavaScript代码已正确注入

### 2. 找不到邮箱元素
- 检查元素选择器是否正确
- Cursor界面可能已更新，需要调整选择器

### 3. 重新注入代码
如果Cursor更新后功能失效，重新运行程序即可重新注入代码。
