{"menu": {"title": "Opzioni Disponibili", "exit": "<PERSON><PERSON><PERSON> dal <PERSON>ma", "reset": "Reimposta ID Macchina", "register": "Registra Nuovo Account Cursor", "register_google": "Registrati con il Tuo Account Google", "register_github": "Registrati con il Tuo Account GitHub", "register_manual": "Registra Cursor con Email <PERSON>", "quit": "Chiudi Applicazione Cursor", "select_language": "Cambia Lingua", "select_chrome_profile": "Seleziona Profilo Chrome", "input_choice": "Inserisci la tua scelta ({choices})", "invalid_choice": "Selezione non valida. Inserisci un numero da {choices}", "program_terminated": "Programma terminato dall'utente", "error_occurred": "Si è verificato un errore: {error}. <PERSON><PERSON><PERSON><PERSON>", "press_enter": "<PERSON><PERSON>", "disable_auto_update": "Disabilita Aggiornamento Automatico di Cursor", "lifetime_access_enabled": "ACCESSO A VITA ABILITATO", "totally_reset": "Reimposta Completamente Cursor", "outdate": "Obsoleto", "temp_github_register": "Registrazione GitHub Temporanea", "admin_required": "Esecuzione come file eseguibile, richiesti privilegi di amministratore.", "admin_required_continue": "Continua senza privilegi di amministratore.", "coming_soon": "Prossimamente", "fixed_soon": "<PERSON><PERSON><PERSON>", "contribute": "Contribuisci al Progetto", "config": "Mostra Configurazione", "delete_google_account": "Elimina Account Google di Cursor", "continue_prompt": "Continuare? (y/N): ", "operation_cancelled_by_user": "Operazione annullata dall'utente", "exiting": "Us<PERSON>ta in corso...", "bypass_version_check": "Ignora Controllo Versione Cursor", "check_user_authorized": "Verifica Autorizzazione Utente", "bypass_token_limit": "Ignora Limite Token", "language_config_saved": "Configurazione lingua salvata con successo", "lang_invalid_choice": "Scelta non valida. Inserisci una delle seguenti opzioni: ({lang_choices})", "restore_machine_id": "Ripristina <PERSON> dal <PERSON>", "manual_custom_auth": "Auth personalizzato manuale"}, "languages": {"ar": "<PERSON><PERSON>", "en": "<PERSON><PERSON><PERSON>", "zh_cn": "Cinese Semplificato", "zh_tw": "Cinese Tradizionale", "vi": "Vietnamita", "nl": "<PERSON><PERSON><PERSON>", "de": "Tedesco", "fr": "<PERSON><PERSON>", "pt": "<PERSON><PERSON><PERSON>", "ru": "<PERSON>", "tr": "<PERSON><PERSON><PERSON>", "bg": "Bulgaro", "es": "<PERSON><PERSON><PERSON>", "ja": "Giapponese", "it": "Italiano"}, "totally_reset": {"warning_title": "AVVERTIMENTO", "delete_input_error": "Errore Trovare Elimina Input: {Errore}", "direct_advanced_navigation": "Provare la navigazione diretta alla scheda avanzata", "delete_input_not_found_continuing": "Elimina l'input di conferma non trovato, cercando di continuare comunque", "advanced_tab_not_found": "Scheda avanzata non trovata dopo più tentativi", "feature_title": "CARATTERISTICHE", "advanced_tab_error": "Errore Trovare la scheda avanzata: {errore}", "disclaimer_title": "DISCLAIMER", "delete_input_not_found": "Elimina l'input di conferma non trovato dopo più tentativi", "disclaimer_6": "Dovrai impostare nuovamente l'intelligenza artificiale del cursore dopo aver eseguito questo strumento.", "note_complete_machine_id_reset_may_require_running_as_administrator": "Nota: il ripristino ID completo della macchina potrebbe richiedere l'esecuzione come amministratore", "failed_to_delete_file": "Impossibile eliminare il file: {Path}", "no_permission": "Impossibile leggere o scrivere il file di configurazione, controlla le autorizzazioni del file", "operation_cancelled": "Operazione annullata. Uscire senza apportare modifiche.", "backup_exists": "Il file di backup esiste gi<PERSON>, saltando il passaggio di backup", "windows_registry_instructions_2": "<PERSON><PERSON><PERSON><PERSON> \"regedit\" e cerca chiavi contenenti \"cursore\" o \"cursori\" sotto hkey_current_user \\ software \\ ed eliminali.", "database_updated_successfully": "Database aggiornato correttamente", "removed": "<PERSON><PERSON><PERSON>: {Path}", "warning_6": "Dovrai impostare nuovamente l'intelligenza artificiale del cursore dopo aver eseguito questo strumento.", "delete_input_retry": "Elimina input non trovato, tentativo {tentativo}/{max_attempts}", "disclaimer_3": "I file di codice non saranno interessati e lo strumento è progettato", "removing_electron_localstorage_files": "Rimozione di file di stivalezione elettronica elettronica", "creating_backup": "Creazione di backup di configurazione", "reset_cancelled": "Rip<PERSON>ina cancellato. Uscire senza apportare modifiche.", "resetting_machine_id": "Ripristina gli identificatori della macchina per bypassare il rilevamento della prova ...", "error_searching": "Errore di ricerca per file in {Path}: {Errore}", "keyboard_interrupt": "Processo interrotto dall'utente. Uscita ...", "cursor_reset_failed": "Reset dell'editor AI del cursore non riuscito: {errore}", "warning_4": "Per target solo i file dell'editor AI del cursore e i meccanismi di rilevamento delle prove.", "skipped_for_safety": "Salta per sicurezza (non correlato al cursore): {Path}", "checking_config": "Controllo del file di configurazione", "feature_3": "Ripristina l'ID macchina per bypassare il rilevamento della prova", "removing_electron_localstorage_files_completed": "Electron LocalStorage Files Rimozione completata", "confirm_3": "I file di codice non saranno interessati e lo strumento è progettato", "reset_log_9": "Se riscontri problemi, vai a GitHub Essument Tracker e crea un problema su https://github.com/yeongpin/cursor-free-vip/issues", "disclaimer_2": "configurazioni e dati memorizzati nella cache. Questa azione non può essere annullata.", "press_enter_to_return_to_main_menu": "Premere Invio per tornare al menu principale ...", "login_redirect_failed": "Reindirizzamento di accesso non riuscito, provando la navigazione diretta ...", "feature_7": "Scansione profonda per licenza nascosta e file relativi alla prova", "linux_machine_id_modification_skipped": "Modifica della macchina Linux-ID saltata: {errore}", "warning_5": "Altre applicazioni sul sistema non saranno interessate.", "reading_config": "Leggendo la configurazione corrente", "feature_6": "Ripristina le informazioni di prova e i dati di attivazione", "reset_log_1": "Il cursore AI è stato completamente ripristinato e il rilevamento delle prove bypassato!", "failed_to_delete_file_or_directory": "Impossibile eliminare file o directory: {Path}", "connected_to_database": "Connesso al database", "removing_known": "Rimozione di file di prova/licenza noti", "return_to_main_menu": "<PERSON><PERSON><PERSON> al <PERSON> principale ...", "found_additional_potential_license_trial_files": "Trovato {Conte} File di licenza/prova potenziali aggiuntivi", "failed_to_delete_directory": "Impossibile eliminare la directory: {Path}", "feature_9": "Compatibile con Windows, MacOS e Linux", "resetting_cursor": "Ripristino dell'editor AI del cursore ... Attendi.", "confirm_1": "Questa azione eliminerà tutte le impostazioni dell'intelligenza artificiale del cursore,", "cursor_reset_completed": "L'editor AI Cursore è stato completamente ripristinato e il rilevamento di prove bypassato!", "warning_3": "I file di codice non saranno interessati e lo strumento è progettato", "advanced_tab_retry": "Scheda avanzata non trovata, tentativo {tentativo}/{max_attempts}", "report_issue": "Si prega di segnalare questo problema a GitHub Issue Tracker all'indirizzo https://github.com/yeongpin/cursor-free-vip/issues", "resetting_cursor_ai_editor": "Ripristino dell'editor AI del cursore ... Attendi.", "electron_localstorage_files_removed": "I file di Electron LocalStorage rimossi", "completed_in": "Completato in {time} secondi", "reset_log_6": "Se disponibile, utilizzare una VPN per modificare il tuo indirizzo IP", "advanced_tab_clicked": "Fare clic sulla scheda Advanced", "delete_button_retry": "Pulsante Elimina non trovato, tentativo {tentativo}/{max_attempts}", "already_on_settings": "Già sulla pagina delle impostazioni", "created_machine_id": "Creato nuovo ID macchina: {Path}", "reset_log_7": "Cancella i cookie e la cache del browser prima di visitare il sito Web del cursore AI", "found_danger_zone": "Sezione di zona di pericolo trovata", "db_not_found": "File di database non trovato su: {Path}", "success": "Il cursore si ripristina correttamente", "config_not_found": "File di configurazione non trovato", "failed_to_remove": "Impossibile rimuovere: {Path}", "performing_deep_scan": "Esecuzione di una scansione profonda per ulteriori file di prova/licenza", "error_deleting": "Errore Eliminazione {Path}: {Errore}", "disclaimer_1": "Questo strumento eliminerà permanentemente tutte le impostazioni dell'intelligenza artificiale del cursore,", "reset_machine_id": "Ripristina ID macchina", "disclaimer_4": "Per target solo i file dell'editor AI del cursore e i meccanismi di rilevamento delle prove.", "disclaimer_7": "Usa a proprio rischio", "windows_machine_id_modification_skipped": "Modifica ID macchina Windows Skipped: {Errore}", "db_connection_error": "Impossibile connettersi al database: {errore}", "reset_log_2": "Si prega di riavviare il sistema per le modifiche per avere effetto.", "feature_2": "Cancella tutti i dati memorizzati nella cache, tra cui cronologia e istruzioni", "windows_registry_instructions": "📝 Nota: per il ripristino completo su <PERSON>, potrebbe anche essere necessario pulire le voci di registro.", "feature_5": "Rimuove estensioni e preferenze personalizzate", "updating_pair": "Aggiornamento della coppia di valore chiave", "feature_4": "Crea nuovi identificatori di macchine randomizzate", "reset_log_3": "Dovrai reinstallare l'intelligenza artificiale del cursore e ora dovresti avere un nuovo periodo di prova.", "failed_to_reset_machine_guid": "Impossibile reimpostare Guid della macchina", "deleted": "Eliminato: {Path}", "error": "Il ripristino del cursore non riuscito: {errore}", "created_extended_trial_info": "Creato nuove informazioni di prova estesa: {Path}", "deep_scanning": "Esecuzione di una scansione profonda per ulteriori file di prova/licenza", "delete_button_clicked": "Clicato sul pulsante Account <PERSON><PERSON>", "db_permission_error": "Impossibile accedere al file di database. Si prega di controllare le autorizzazioni", "title": "Ripristina totalmente il cursore", "no_additional_license_trial_files_found_in_deep_scan": "Nessun file di licenza/tentativi aggiuntivi trovati nella scansione profonda", "process_interrupted": "Processo interrotto. Uscita ...", "electron_localstorage_files_removal_error": "Errore Rimozione dei file di Electron LocalStorage: {Errore}", "checking_for_electron_localstorage_files": "Controlla i file di Electron LocalStorage", "reset_log_8": "Se i problemi persistono, prova a installare AI del cursore in una posizione diversa", "warning_7": "Usa a proprio rischio", "reset_log_5": "Utilizzare un indirizzo email diverso quando si registra per una nuova prova", "press_enter": "Premere Invio per uscire", "disclaimer_5": "Altre applicazioni sul sistema non saranno interessate.", "generating_new_machine_id": "Generazione di un nuovo ID macchina", "feature_1": "Rimozione completa delle impostazioni e delle configurazioni del cursore AI", "error_creating_trial_info": "Errore creazione del file di informazioni di prova {path}: {errore}", "note_complete_system_machine_id_reset_may_require_sudo_privileges": "Nota: il ripristino ID macchina completo di sistema può richiedere privilegi sudo", "delete_button_not_found": "Elimina il pulsante dell'account non trovato dopo più tentativi", "reset_log_4": "Per i migliori risultati, considera anche:", "delete_button_error": "Errore Trovare il pulsante Elimina: {errore}", "confirm_7": "Usa a proprio rischio", "confirm_2": "configurazioni e dati memorizzati nella cache. Questa azione non può essere annullata.", "unexpected_error": "Si è verificato un errore imprevisto: {errore}", "feature_8": "Preserva in modo sicuro file e applicazioni non corsorio", "confirm_title": "Sei sicuro di voler procedere?", "saving_new_config": "Salvare la nuova configurazione su JSON", "not_found": "File non trovato: {path}", "warning_1": "Questa azione eliminerà tutte le impostazioni dell'intelligenza artificiale del cursore,", "warning_2": "configurazioni e dati memorizzati nella cache. Questa azione non può essere annullata.", "error_creating_machine_id": "Errore creazione del file ID macchina {Path}: {Errore}", "confirm_4": "Per target solo i file dell'editor AI del cursore e i meccanismi di rilevamento delle prove.", "confirm_5": "Altre applicazioni sul sistema non saranno interessate.", "database_connection_closed": "Connessione del database chiuso", "confirm_6": "Dovrai impostare nuovamente l'intelligenza artificiale del cursore dopo aver eseguito questo strumento.", "navigating_to_settings": "Navigazione alla pagina delle impostazioni ...", "invalid_choice": "Inserisci 'y' o 'n'", "cursor_reset_cancelled": "Cursore Editor AI reset cancellato. Uscire senza apportare modifiche."}, "oauth": {"no_chrome_profiles_found": "Nessun profilo Chrome trovato, usando il valore predefinito", "starting_new_authentication_process": "Avvio di un nuovo processo di autenticazione ...", "failed_to_delete_account": "Impossibile eliminare l'account: {errore}", "found_email": "Email trovata: {email}", "github_start": "Github inizia", "already_on_settings_page": "Già sulla pagina delle impostazioni!", "starting_github_authentication": "Autenticazione GitHub iniziale ...", "status_check_error": "Errore di controllo dello stato: {errore}", "account_is_still_valid": "L'account è ancora valido (Utilizzo: {Utilizzo})", "authentication_timeout": "Timeout di autenticazione", "using_first_available_chrome_profile": "Utilizzando il primo profilo Chrome disponibile: {profilo}", "google_start": "Google Start", "usage_count": "Conteggio di utilizzo: {utilizzo}", "no_compatible_browser_found": "Nessun browser compatibile trovato. Si prega di installare Google Chrome o Chromium.", "authentication_successful_getting_account_info": "Autenticazione riuscita, ottenendo informazioni sull'account ...", "found_chrome_at": "Trovato Chrome a: {Path}", "error_getting_user_data_directory": "Errore per ottenere la directory dei dati dell'utente: {errore}", "error_finding_chrome_profile": "Errore Trovare il profilo Chrome, usando impostazione predefinita: {errore}", "auth_update_success": "AUTTH AGGIORNAMENTO SUCCESSO", "authentication_successful": "Autenticazione riuscita - email: {email}", "authentication_failed": "Autenticazione non riuscita: {errore}", "warning_browser_close": "Avvertenza: questo chiuderà tutti i processi in esecuzione {browser}", "supported_browsers": "Browser supportati per {piattaforma}", "authentication_button_not_found": "Pulsante di autenticazione non trovato", "starting_new_google_authentication": "Avvio di una nuova autenticazione di Google ...", "waiting_for_authentication": "Aspettando l'autenticazione ...", "found_default_chrome_profile": "Trovato Profilo Chrome predefinito", "starting_browser": "Browser iniziale su: {path}", "token_extraction_error": "Errore di estrazione token: {errore}", "could_not_check_usage_count": "Impossibile controllare il conteggio dell'utilizzo: {errore}", "profile_selection_error": "Errore durante la selezione del profilo: {errore}", "warning_could_not_kill_existing_browser_processes": "ATTENZIONE: Impossibile uccidere i processi del browser esistenti: {errore}", "browser_failed_to_start": "Il browser non è stato avviato: {errore}", "redirecting_to_authenticator_cursor_sh": "Reindirizzamento ad Authenticator.cursor.sh ...", "starting_re_authentication_process": "Avvio del processo di re-autenticazione ...", "found_browser_data_directory": "Directory dei dati del browser trovata: {Path}", "browser_not_found_trying_chrome": "Impossibile trovare {browser}, provando invece Chrome", "found_cookies": "Trovati {Count} Cookies", "auth_update_failed": "Aggiornamento dell'autenticazione non riuscita", "browser_failed_to_start_fallback": "Il browser non è stato avviato: {errore}", "failed_to_delete_expired_account": "Impossibile eliminare il conto scaduto", "navigating_to_authentication_page": "Navigazione alla pagina di autenticazione ...", "initializing_browser_setup": "Inizializzazione della configurazione del browser ...", "browser_closed": "Browser chiuso", "failed_to_delete_account_or_re_authenticate": "Impossibile eliminare l'account o ri-autenticato: {errore}", "detected_platform": "Piattaforma rilevata: {piattaforma}", "failed_to_extract_auth_info": "Impossibile estrarre le informazioni di autenticazione: {errore}", "starting_google_authentication": "Avvio dell'autenticazione di Google ...", "browser_failed": "Il browser non è stato avviato: {errore}", "using_browser_profile": "Utiliz<PERSON>do il profilo del browser: {profilo}", "consider_running_without_sudo": "Considera l'esecuzione dello script senza sudo", "try_running_without_sudo_admin": "Prova a correre senza privilegi sudo/amministratore", "page_changed_checking_auth": "<PERSON><PERSON>a cambiata, controllando l'auth ...", "running_as_root_warning": "L'esecuzione come root non è consigliato per l'automazione del browser", "please_select_your_google_account_to_continue": "Seleziona il tuo account Google per continuare ...", "browser_setup_failed": "Impostazione del browser non riuscita: {errore}", "missing_authentication_data": "Dati di autenticazione mancanti: {data}", "using_configured_browser_path": "Utilizzando il percorso configurato {browser}: {path}", "killing_browser_processes": "Uccidere {browser} processi ...", "could_not_find_usage_count": "Impossibile trovare il conteggio degli utili: {errore}", "browser_setup_completed": "Configurazione del browser completato correttamente", "account_has_reached_maximum_usage": "L'account ha raggiunto il massimo utilizzo, {eliminazione}", "could_not_find_email": "Impossibile trovare e -mail: {errore}", "user_data_dir_not_found": "{browser} Directory di dati utente non trovata su {Path}, proverà invece Chrome", "found_browser_user_data_dir": "Trovato {browser} directory dei dati utente: {Path}", "invalid_authentication_type": "Tipo di autenticazione non valido"}, "manual_auth": {"auth_type_selected": "Tipo di autenticazione selezionato: {type}", "proceed_prompt": "Procedere? (y/n):", "auth_type_github": "<PERSON><PERSON><PERSON>", "confirm_prompt": "Si prega di confermare le seguenti informazioni:", "invalid_token": "Token non valido. L'autenticazione ha interrotto.", "continue_anyway": "Continua comunque? (y/n):", "token_verified": "Token ha verificato con successo!", "error": "Errore: {errore}", "auth_update_failed": "Impossibile aggiornare le informazioni di autenticazione", "auth_type_prompt": "Seleziona Tipo di autenticazione:", "auth_type_auth0": "Auth_0 (impostazione predefinita)", "verifying_token": "Verificare la validità del segno ...", "auth_updated_successfully": "Informazioni di autenticazione aggiornate con successo!", "email_prompt": "Inserisci e -mail (lascia vuoto per e -mail casuale):", "token_prompt": "Inserisci il token del cursore (Access_Token/Refrigera_Token):", "title": "Autenticazione del cursore manuale", "token_verification_skipped": "Verifica token Skipped (check_user_authorized.py non trovato)", "random_email_generated": "Email casuale generata: {email}", "token_required": "È richiesto il token", "auth_type_google": "Google", "operation_cancelled": "Operazione annullata", "token_verification_error": "Errore Verifica del token: {errore}", "updating_database": "Aggiornamento del database di autenticazione del cursore ..."}, "reset": {"version_parse_error": "Errore di analisi della versione: {errore}", "sqlite_error": "Aggiornamento del database SQLite non riuscito: {errore}", "patch_failed": "Patching getmachineid non riuscito: {errore}", "version_too_low": "Versione del cursore troppo basso: {versione} <0.45.0", "backup_exists": "Il file di backup esiste gi<PERSON>, saltando il passaggio di backup", "update_success": "Aggiorna il successo", "update_windows_machine_id_failed": "Aggiorna ID macchina Windows non riuscito: {errore}", "sqlite_success": "Database SQLite aggiornato correttamente", "check_version_failed": "Controlla la versione non riuscita: {errore}", "updating_pair": "Aggiornamento della coppia di valore chiave", "windows_machine_guid_updated": "Windows Machine GUID aggiornato correttamente", "file_modified": "File modificato", "found_version": "Versione trovata: {versione}", "start_patching": "Iniziare a patching getmachineid", "updating_sqlite": "Aggiornamento del database SQLite", "backup_created": "Backup creato", "invalid_json_object": "Oggetto JSON non valido", "detecting_version": "Rilevamento della versione del cursore", "update_failed": "Aggiornamento non riuscito: {errore}", "version_field_empty": "Il campo versione è vuoto", "run_as_admin": "Prova a eseguire questo programma come amministratore", "windows_permission_denied": "Autorizzazione di Windows Negata", "saving_json": "Salvare la nuova configurazione su JSON", "linux_path_not_found": "Percorso Linux non trovato", "invalid_version_format": "Formato versione non valida: {versione}", "path_not_found": "Per<PERSON>so non trovato: {path}", "windows_machine_id_updated": "ID macchina Windows Aggiornato correttamente", "creating_backup": "Creazione di backup di configurazione", "stack_trace": "Traccia dello stack", "no_version_field": "<PERSON><PERSON>un campo versione trovato in pack.json", "title": "Strumento di ripristino ID macchina cursore", "system_ids_update_failed": "Aggiornamento IDS di sistema non riuscito: {errore}", "plutil_command_failed": "comando plutil non riuscito", "version_check_passed": "Controllo della versione del cursore Passato", "updating_system_ids": "Aggiornamento degli ID di sistema", "unsupported_os": "OS non supportato: {OS}", "macos_uuid_update_failed": "Aggiornamento UUID macos non riuscito", "windows_guid_updated": "Windows GUID aggiornato correttamente", "windows_guid_update_failed": "Aggiornamento di Windows GUID non riuscito", "no_permission": "Impossibile leggere o scrivere il file di configurazione, controlla le autorizzazioni del file", "package_not_found": "Pacchetto.json non trovato: {path}", "not_found": "File di configurazione non trovato", "update_windows_machine_guid_failed": "Aggiorna Windows Machine GUID non riuscito: {errore}", "system_ids_updated": "ID di sistema aggiornati correttamente", "patch_completed": "Patching getmachineid completato", "no_write_permission": "Nessuna autorizzazione di scrittura: {Path}", "current_version": "Versione del cursore corrente: {versione}", "patching_getmachineid": "Patching getmachineid", "reading_package_json": "Lettura pacchetto.json {Path}", "permission_error": "Errore di autorizzazione: {errore}", "generating": "Generazione di un nuovo ID macchina", "macos_uuid_updated": "macos uuid aggiornato correttamente", "new_id": "Nuovo ID macchina", "reading": "Leggendo la configurazione corrente", "permission_denied": "Autorizzazione negata: {errore}", "version_greater_than_0_45": "Versione del cursore> = 0.45.0, patching getmachineid", "checking": "Controllo del file di configurazione", "success": "ID macchina ripristina correttamente", "press_enter": "Premere Invio per uscire", "process_error": "Errore di processo di ripristino: {errore}", "file_not_found": "File non trovato: {path}", "version_less_than_0_45": "Versione del cursore <0,45,0, salta patching getmachineid", "modify_file_failed": "Modifica il file non riuscito: {errore}"}, "auth_check": {"token_length": "Lunghezza token: {lunghezza} caratteri", "usage_response_status": "Stato di risposta di utilizzo: {risposta}", "operation_cancelled": "Operazione annullata dall'utente", "error_getting_token_from_db": "Errore per ottenere token dal database: {errore}", "checking_usage_information": "Controllo delle informazioni sull'utilizzo ...", "usage_response": "Risposta di utilizzo: {risposta}", "authorization_failed": "L'autorizzazione è fallita!", "authorization_successful": "Autorizzazione di successo!", "check_error": "Autorizzazione del controllo degli errori: {errore}", "request_timeout": "Richie<PERSON> scaduta", "connection_error": "Errore di connessione", "invalid_token": "Token non valido", "check_usage_response": "Controlla l'utilizzo di risposta: {risposta}", "enter_token": "Inserisci il token del cursore:", "user_unauthorized": "L'utente non è autorizzato", "token_found_in_db": "Token trovato nel database", "checking_authorization": "Controllo dell'autorizzazione ...", "error_generating_checksum": "Errore che genera checksum: {errore}", "token_source": "Ottieni il token dal database o dall'input manualmente? (d/m, impostazione predefinita: d)", "unexpected_error": "Errore imprevisto: {errore}", "user_authorized": "L'utente è autorizzato", "token_not_found_in_db": "Token non trovato nel database", "jwt_token_warning": "Il token sembra essere in formato JWT, ma il controllo API ha restituito un codice di stato imprevisto. Il token potrebbe essere valido ma l'accesso API è limitato.", "unexpected_status_code": "Codice di stato imprevisto: {codice}", "getting_token_from_db": "Ottenere il token dal database ...", "cursor_acc_info_not_found": "CURSOR_ACC_INFO.PY non trovato"}, "account_delete": {"delete_input_not_found": "Elimina l'input di conferma non trovato dopo più tentativi", "logging_in": "Accesso con Google ...", "confirm_button_not_found": "Conferma il pulsante non trovato dopo più tentativi", "confirm_button_error": "Errore Trovare il pulsante di conferma: {errore}", "delete_button_clicked": "Clicato sul pulsante Account <PERSON><PERSON>", "confirm_prompt": "Sei sicuro di voler procedere? (y/n):", "delete_button_error": "Errore Trovare il pulsante Elimina: {errore}", "cancelled": "Eliminazione dell'account annullata.", "interrupted": "Processo di eliminazione dell'account interrotto dall'utente.", "error": "Errore durante la cancellazione dell'account: {errore}", "delete_input_not_found_continuing": "Elimina l'input di conferma non trovato, cercando di continuare comunque", "advanced_tab_retry": "Scheda avanzata non trovata, tentativo {tentativo}/{max_attempts}", "waiting_for_auth": "Aspettando l'autenticazione di Google ...", "typed_delete": "<PERSON><PERSON><PERSON> \"<PERSON><PERSON>\" nella casella di conferma", "trying_settings": "Cercando di navigare alla pagina delle impostazioni ...", "delete_input_retry": "Elimina input non trovato, tentativo {tentativo}/{max_attempts}", "email_not_found": "E -mail non trovata: {errore}", "delete_button_not_found": "Elimina il pulsante dell'account non trovato dopo più tentativi", "already_on_settings": "Già sulla pagina delle impostazioni", "failed": "Processo di eliminazione dell'account non riuscito o è stato annullato.", "warning": "ATTENZIONE: questo eliminerà permanentemente il tuo account cursore. Questa azione non può essere annullata.", "direct_advanced_navigation": "Provare la navigazione diretta alla scheda avanzata", "advanced_tab_not_found": "Scheda avanzata non trovata dopo più tentativi", "auth_timeout": "Timeout di autenticazione, continuando comunque ...", "select_google_account": "Seleziona il tuo account Google ...", "google_button_not_found": "Pulsante di accesso Google non trovato", "found_danger_zone": "Sezione di zona di pericolo trovata", "account_deleted": "Account eliminato con successo!", "starting_process": "Processo di eliminazione dell'account di avvio ...", "advanced_tab_error": "Errore Trovare la scheda avanzata: {errore}", "delete_button_retry": "Pulsante Elimina non trovato, tentativo {tentativo}/{max_attempts}", "login_redirect_failed": "Reindirizzamento di accesso non riuscito, provando la navigazione diretta ...", "unexpected_error": "Errore imprevisto: {errore}", "delete_input_error": "Errore Trovare Elimina Input: {Errore}", "login_successful": "Accedi di successo", "advanced_tab_clicked": "Fare clic sulla scheda Advanced", "unexpected_page": "Pagina imprevisto dopo l'accesso: {url}", "found_email": "Email trovata: {email}", "title": "Cursore Strumento di cancellazione dell'account Google", "navigating_to_settings": "Navigazione alla pagina delle impostazioni ...", "success": "Il tuo account Cursore è stato eliminato con successo!", "confirm_button_retry": "Conferma il pulsante non trovato, tentativo {tentativo}/{max_attempts}"}, "token": {"refreshing": "Token rinfrescante ...", "extraction_error": "Errore di estrazione del token: {errore}", "invalid_response": "Risposta JSON non valida dal server di aggiornamento", "no_access_token": "Nessun token di accesso in risposta", "connection_error": "Errore di connessione per aggiornare il server", "unexpected_error": "Errore imprevisto durante l'aggiornamento del token: {errore}", "server_error": "Aggiorna errore del server: http {status}", "refresh_failed": "Token Afto non riuscito: {errore}", "refresh_success": "Token rinfrescato con successo! Valido per {giorni} gior<PERSON> (scade: {scadere})", "request_timeout": "Richiesta di aggiornare il server timed out"}, "register": {"cursor_auth_info_updated": "INFO AUTH CURSOR AGGIORNATE", "no_turnstile": "Non rilevare il tornello", "password_submitted": "Password inviata", "using_browser": "Utilizzando {browser} browser: {Path}", "could_not_track_processes": "Impossibile tracciare i processi {browser}: {errore}", "total_usage": "Utilizzo totale: {utilizzo}", "open_mailbox": "Apertura della pagina della cassetta postale", "verification_timeout": "Ottieni timeout del codice di verifica", "config_updated": "Configurazione aggiornata", "form_submitted": "<PERSON><PERSON>lo inviato, avvia verifica ...", "verification_error": "Errore di verifica: {errore}", "setting_password": "Impostazione della password", "verification_code_filled": "Codice di verifica riempito", "try_install_browser": "Prova a installare il browser con il tuo gestore di pacchetti", "detect_turnstile": "Verifica della verifica della sicurezza ...", "tempmail_plus_verification_started": "Avvio del processo di verifica TempMailPlus", "account_error": "Ottieni informazioni sull'account non riuscita: {errore}", "setting_on_password": "Impostazione della password", "token_attempt": "Provare | {tentativo} volte per ottenere token | Ritte<PERSON>à in {time} s", "start_getting_verification_code": "Inizia a ottenere il codice di verifica, proverò negli anni '60", "max_retries_reached": "I tentativi di pensionamento massimi raggiunti. La registrazione non è riuscita.", "starting_browser": "Browser di apertura ...", "email_address": "Indirizzo e-mail", "tempmail_plus_enabled": "TempmailPlus è abilitato", "turnstile_passed": "Il Turnstile passò", "manual_email_input": "Input e -mail manuale", "filling_form": "Forma di riempimento", "browser_path_invalid": "Il percorso {browser} non è valido, usando il percorso predefinito", "get_email_address": "<PERSON><PERSON><PERSON> indirizzo email", "human_verify_error": "Non è possibile verificare che l'utente sia umano. Riprovare ...", "update_cursor_auth_info": "Aggiorna le informazioni sull'auth del cursore", "browser_started": "Il browser ha aperto con successo", "try_get_code": "Provare | {tentativo} Ottieni codice di verifica | Tempo rim<PERSON>nte: {time} s", "password_error": "Impossibile impostare la password: {errore}. Per <PERSON>e riprova", "manual_code_input": "Input del codice manuale", "retry_verification": "Riprovare la verifica ...", "token_max_attempts": "Reach Max Tentations ({max}) | Non è riuscito a ottenere il token", "setup_error": "Errore di configurazione e -mail: {errore}", "using_tempmail_plus": "Utilizzo di TempmailPlus per la verifica della posta elettronica", "config_created": "Configurazione creata", "try_get_verification_code": "<PERSON>vare | {tentativo} Ottieni codice di verifica | Tempo rim<PERSON>nte: {restaning_time} s", "cursor_registration_completed": "Registrazione del cursore completato!", "verification_failed": "Verifica non riuscita", "tracking_processes": "Tracciamento {count} {browser} processi", "tempmail_plus_epin_missing": "TempmailPlus Epin non è configurato", "visiting_url": "URL in visita", "tempmail_plus_verification_failed": "Verifica tempmailplus non riuscita: {errore}", "verification_success": "Verifica di sicurezza con successo", "using_browser_profile": "Utilizzando il profilo {browser} da: {user_data_dir}", "reset_machine_id": "Ripristina ID macchina", "handling_turnstile": "Elaborazione della verifica della sicurezza ...", "get_token": "Ottieni il token della sessione del cursore", "login_success_and_jump_to_settings_page": "Accedi al successo e passa alla pagina delle impostazioni", "tempmail_plus_verification_completed": "TempmailPlus Verification completata correttamente", "waiting_for_second_verification": "In attesa di verifica della posta elettronica ...", "basic_info": "Informazioni di base inviate", "verification_start": "Inizia a ottenere il codice di verifica", "password": "Password", "title": "Strumento di registrazione del cursore", "tempmail_plus_email_missing": "TempmailPlus Email non è configurato", "browser_start": "Browser iniziale", "tempmail_plus_config_missing": "Manca la configurazione TempmailPlus", "waiting_for_page_load": "Pagina di caricamento ...", "get_verification_code_success": "Ottieni il successo del codice di verifica", "tempmail_plus_init_failed": "Impossibile inizializzare tempmailplus: {errore}", "tempmail_plus_initialized": "TempmailPlus inizializzato correttamente", "account_info_saved": "Informazioni sul conto salvate", "token_success": "Ottieni il successo di token", "register_start": "Inizia Registrati", "cursor_auth_info_update_failed": "Cursore Aggiornamento Info Auth non riuscito", "form_success": "<PERSON><PERSON><PERSON> inviato con successo", "basic_info_submitted": "Informazioni di base inviate", "tempmail_plus_disabled": "TempmailPlus è disabilitato", "handle_turnstile": "Maneggiare il turno", "config_option_added": "Opzione di configurazione aggiunta: {opzione}", "start": "Processo di registrazione iniziale ...", "get_verification_code_timeout": "Ottieni timeout del codice di verifica", "detect_login_page": "Rileva la pagina di accesso, inizia l'accesso ...", "register_process_error": "Errore di processo di registrazione: {errore}", "no_new_processes_detected": "Nessun nuovo processo {browser} rilevati per tracciare", "mailbox": "Accesso alla casella di posta elettronica accessibile correttamente", "first_name": "Nome di battesimo", "email_error": "Impossibile ottenere l'indirizzo e -mail", "exit_signal": "<PERSON><PERSON><PERSON> di uscita", "token_failed": "Ottieni token non riuscito: {errore}", "verification_not_found": "Nessun codice di verifica trovato", "save_account_info_failed": "Salva informazioni sull'account non riuscito", "password_success": "Imposta password correttamente", "getting_code": "Ottenere il codice di verifica, proverò negli anni '60", "last_name": "Cognome", "first_verification_passed": "Verifica iniziale riuscita", "get_account": "Ottenere informazioni sull'account", "press_enter": "Premere Invio per uscire", "make_sure_browser_is_properly_installed": "Assicurati che {browser} sia installato correttamente", "set_password": "Imposta password", "waiting_for_verification_code": "In attesa del codice di verifica ..."}, "quit_cursor": {"timeout": "Timeout del processo: {pids}", "error": "Si è verificato un errore: {errore}", "start": "Inizia a smettere di cursore", "terminating": "Processo di terminazione {pid}", "success": "<PERSON>tti i processi del cursore sono chiusi", "waiting": "Aspettando l'uscita del processo", "no_process": "Nessun processo di cursore in esecuzione"}, "browser_profile": {"profile_selected": "Profilo se<PERSON>o: {profilo}", "default_profile": "<PERSON><PERSON>", "no_profiles": "No {browser} profili trovati", "select_profile": "Seleziona il profilo {browser} da utilizzare:", "error_loading": "Errore Caricamento {<PERSON><PERSON><PERSON>} Profili: {Errore}", "invalid_selection": "Selezione non valida. Per favore riprova.", "title": "Selezione del profilo del browser", "profile": "Profilo {numero}", "profile_list": "Disponibile {browser} Profili:"}, "email": {"refresh_error": "Errore di aggiornamento e -mail: {errore}", "verification_code_found": "Codice di verifica trovata", "no_display_found": "Nessun display trovato. Assicurati che X Server sia in esecuzione.", "try_export_display": "Prova: display di esportazione =: 0", "try_install_chromium": "Prova: sudo APT Installa il browser Chromium", "blocked_domains": "<PERSON><PERSON> bloccati: {domini}", "blocked_domains_loaded_timeout_error": "Domati bloccati Errore di timeout caricato: {errore}", "create_failed": "Impossibile creare e -mail", "switching_service": "Passa al servizio {Service}", "refreshing": "<PERSON>ail rin<PERSON>", "blocked_domains_loaded_success": "Domini bloccati caricati correttamente", "verification_not_found": "Verifica non trovata", "verification_error": "Errore di verifica: {errore}", "starting_browser": "Browser iniziale", "failed_to_get_available_domains": "Impossibile ottenere domini disponibili", "domains_excluded": "<PERSON><PERSON> esclusi: {domini}", "verification_found": "Verifica trovata", "visiting_site": "Visitare i domini della posta", "verification_code_not_found": "Codice di verifica non trovato", "extension_load_error": "Errore di carico di estensione: {errore}", "refresh_success": "Email a<PERSON> con <PERSON>o", "available_domains_loaded": "Domini disponibili caricati: {count}", "blocked_domains_loaded_error": "Errore caricato domini bloccato: {errore}", "create_success": "Email creata correttamente", "make_sure_chrome_chromium_is_properly_installed": "Assicurati che Chrome/Chromium sia installato correttamente", "blocked_domains_loaded_timeout": "Timeout caricato domini bloccato: {timeout} s", "create_error": "Errore di creazione e -mail: {errore}", "domains_filtered": "<PERSON><PERSON> filtrati: {count}", "account_creation_error": "Errore di creazione dell'account: {errore}", "domains_list_error": "Impossibile ottenere l'elenco dei domini: {errore}", "no_available_domains_after_filtering": "Nessun domini disponibili dopo il filtro", "trying_to_create_email": "Cercando di creare email: {email}", "domain_blocked": "Dominio bloccato: {dominio}", "failed_to_create_account": "Impossibile creare un account", "refresh_button_not_found": "Pulsante di aggiornamento non trovato", "address": "Indirizzo e-mail", "using_chrome_profile": "Utilizzo del profilo Chrome da: {user_data_dir}", "blocked_domains_loaded": "<PERSON>ini bloccati caricati: {count}", "verification_code_error": "Errore del codice di verifica: {errore}", "all_domains_blocked": "<PERSON>tti i domini hanno bloccato il servizio di commutazione"}, "github_register": {"feature2": "Registra un nuovo account GitHub con credenziali casuali.", "feature6": "<PERSON>va tutte le credenziali in un file.", "starting_automation": "Automazione iniziale ...", "feature1": "Genera un'e -mail temporanea utilizzando 1secmail.", "title": "GitHub + Cursor AI Registration Automation", "github_username": "GitHub Nome utente", "check_browser_windows_for_manual_intervention_or_try_again_later": "Controlla le finestre del browser per l'intervento manuale o riprova più tardi.", "warning1": "Questo script automatizza la creazione di account, che può violare i termini di servizio GitHub/cursore.", "feature4": "Accedi all'intelligenza artificiale del cursore usando l'autenticazione GitHub.", "invalid_choice": "Scelta non valida. <PERSON><PERSON><PERSON><PERSON> 'sì' o 'no'", "completed_successfully": "Registrazione del cursore GitHub + completato con successo!", "warning2": "Richiede i privilegi di accesso a Internet e amministrativi.", "registration_encountered_issues": "La registrazione del cursore GitHub + ha riscontrato problemi.", "credentials_saved": "Queste credenziali sono state salvate su github_cursor_accounts.txt", "feature3": "Verifica automaticamente l'e -mail GitHub.", "github_password": "Password GitHub", "features_header": "<PERSON><PERSON><PERSON><PERSON>", "feature5": "Reimposta l'ID macchina per bypassare il rilevamento della prova.", "warning4": "Usa in modo responsabile e a proprio rischio.", "warning3": "CAPTCHA o una verifica aggiuntiva può interrompere l'automazione.", "cancelled": "Operazione annullata", "warnings_header": "Avvertimenti", "program_terminated": "Programma terminato dall'utente", "confirm": "Sei sicuro di voler procedere?", "email_address": "Indirizzo e-mail"}, "restore": {"current_file_not_found": "File di archiviazione corrente non trovato", "please_enter_number": "Inserisci un numero valido", "starting": "Avvio del processo di ripristino dell'ID macchina", "sqlite_not_found": "Database SQLite non trovato", "machine_id_updated": "MachineID File aggiornato correttamente", "update_failed": "Impossibile aggiornare il file di archiviazione: {errore}", "updating_pair": "Aggiornamento della coppia di valore chiave", "to_cancel": "per annullare", "sqlite_update_failed": "Impossibile aggiornare il database SQLite: {errore}", "read_backup_failed": "Impossibile leggere il file di backup: {errore}", "invalid_selection": "Selezione non valida", "system_ids_update_failed": "Impossibile aggiornare gli ID di sistema: {errore}", "backup_creation_failed": "Impossibile creare backup: {errore}", "updating_system_ids": "Aggiornamento degli ID di sistema", "update_windows_machine_guid_failed": "Impossibile aggiornare Windows Machine Guid: {Errore}", "update_windows_system_ids_failed": "Impossibile aggiornare gli ID di sistema di Windows: {errore}", "sqlite_updated": "Database SQLite aggiornato correttamente", "update_windows_machine_id_failed": "Impossibile aggiornare ID macchina Windows: {Errore}", "storage_updated": "File di archiviazione aggiornato correttamente", "missing_id": "ID mancante: {id}", "success": "ID macchina ripristinato correttamente", "machine_id_backup_created": "Backup creato del file MachineId", "machine_id_update_failed": "Impossibile aggiornare il file machineid: {errore}", "windows_machine_id_updated": "ID macchina Windows Aggiornato correttamente", "ids_to_restore": "ID macchina per ripristinare", "current_backup_created": "Creato backup del file di archiviazione corrente", "select_backup": "Sele<PERSON>na Backup per ripristinare", "operation_cancelled": "Operazione annullata", "press_enter": "Premere Invio per continuare", "process_error": "Restore Errore del processo: {errore}", "confirm": "Sei sicuro di voler ripristinare questi ID?", "macos_platform_uuid_updated": "piattaforma macOS UUID aggiornata correttamente", "failed_to_execute_plutil_command": "Impossibile eseguire il comando plutil", "update_macos_system_ids_failed": "Impossibile aggiornare ID di sistema macOS: {errore}", "sqm_client_key_not_found": "Chiave di registro sqmclient non trovata", "title": "Ripristina ID macchina dal backup", "windows_machine_guid_updated": "Windows Machine GUID aggiornato correttamente", "permission_denied": "Permesso negato. Prova a correre come amministratore", "no_backups_found": "Nessun file di backup trovato", "available_backups": "File di backup disponibili", "updating_sqlite": "Aggiornamento del database SQLite"}, "account_info": {"subscription": "Sottoscrizione", "failed_to_get_account_info": "Impossibile ottenere le informazioni sull'account", "subscription_type": "Tipo di abbonamento", "pro": "Pro", "failed_to_get_account": "Impossibile ottenere le informazioni sull'account", "config_not_found": "Configurazione non trovata.", "premium_usage": "Utilizzo premium", "failed_to_get_subscription": "Impossibile ottenere informazioni sull'abbonamento", "basic_usage": "Uso di base", "premium": "Premium", "free": "<PERSON><PERSON><PERSON><PERSON>", "email_not_found": "E -mail non trovata", "title": "Informazioni sull'account", "inactive": "Inattivo", "remaining_trial": "<PERSON><PERSON>", "enterprise": "Impresa", "lifetime_access_enabled": "Accesso a vita abilitato", "failed_to_get_usage": "Impossibile ottenere informazioni sull'utilizzo", "usage_not_found": "Utilizzo non trovato", "days_remaining": "<PERSON><PERSON><PERSON>", "failed_to_get_token": "Non è riuscito a ottenere il token", "token": "Gettone", "subscription_not_found": "Informazioni di abbonamento non trovate", "days": "<PERSON>ior<PERSON>", "team": "Squadra", "token_not_found": "Token non trovato", "pro_trial": "Prova pro", "active": "Attivo", "email": "E-mail", "failed_to_get_email": "Impossibile ottenere l'indirizzo e -mail", "trial_remaining": "Residente processo professionale", "usage": "<PERSON><PERSON><PERSON><PERSON>"}, "updater": {"development_version": "<PERSON>e di sviluppo {corrente}> {ultimo}", "check_failed": "Impossibile verificare gli aggiornamenti: {errore}", "update_skipped": "Saltare l'aggiornamento.", "update_confirm": "Vuoi aggiornare all'ultima versione? (Y/n)", "up_to_date": "Stai usando l'ultima versione.", "changelog_title": "Changelog", "new_version_available": "Nuova versione disponibile! (Corrente: {corrente}, ultimo: {ultimo})", "updating": "Aggiornamento all'ultima versione. Il programma si riavvierà automaticamente.", "rate_limit_exceeded": "Limite di tasso API GitHub superato. Stipping Aggiornamento Controllo.", "invalid_choice": "Scelta non valida. Inserisci 'y' o 'n'.", "checking": "Controllare gli aggiornamenti ...", "continue_anyway": "Continuando con la versione attuale ..."}, "update": {"clearing_update_yml": "Cancellatura del file update.yml", "press_enter": "Premere Invio per uscire", "update_yml_cleared": "File aggiornato.yml cancellato", "disable_success": "Aggiornamento automatico disabilitato correttamente", "start_disable": "Inizia a disabilitare l'aggiornamento automatico", "removing_directory": "Rimozione della directory", "unsupported_os": "OS non supportato: {System}", "block_file_already_locked": "Il file di blocco è già bloccato", "yml_already_locked_error": "File aggiornato.yml Errore già bloccato: {errore}", "update_yml_not_found": "File aggiornato.yml non trovato", "block_file_created": "Blocca il file creato", "yml_locked_error": "Errore bloccato del file update.yml: {errore}", "remove_directory_failed": "Impossibile rimuovere la directory: {errore}", "yml_already_locked": "Il file update.yml è già bloccato", "create_block_file_failed": "Impossibile creare file block: {errore}", "block_file_locked_error": "Blocca Errore bloccato del file: {errore}", "killing_processes": "Uccidimento dei processi", "directory_locked": "La directory è bloccata: {Path}", "block_file_already_locked_error": "Blocca il file già bloccato errore: {errore}", "creating_block_file": "Creazione di file di blocco", "clear_update_yml_failed": "Impossibile cancellare il file update.yml: {errore}", "yml_locked": "Il file update.yml è bloccato", "block_file_locked": "Il file di blocco è bloccato", "processes_killed": "<PERSON>i uccisi", "title": "Disabilita aggiornamento automatico del cursore", "disable_failed": "Disabilita l'aggiornamento automatico non riuscito: {errore}", "directory_removed": "<PERSON> rimosso"}, "control": {"get_email_name_success": "Ottieni il successo del nome e -mail", "get_email_address": "<PERSON><PERSON><PERSON> indirizzo email", "blocked_domain": "<PERSON><PERSON><PERSON> bloc<PERSON>o", "navigate_to": "Navigare a {url}", "token_saved_to_file": "Token salvato su cursore_tokens.txt", "get_cursor_session_token_success": "Ottieni il successo del token di sessione di cursore", "no_valid_verification_code": "Nessun codice di verifica valido", "verification_found": "Codice di verifica trovata", "get_email_name": "Ottieni nome e -mail", "get_email_address_success": "Ottieni il successo dell'indirizzo e -mail", "verification_not_found": "Nessun codice di verifica trovato", "copy_email": "Copia dell'indirizzo e -mail", "select_domain": "Selezione del dominio casuale", "select_email_domain": "Seleziona Dominio e -mail", "found_verification_code": "Codice di verifica trovato", "refresh_mailbox": "Casella di posta rinfrescante", "generate_email_success": "Generare successo e -mail", "enter_mailbox_success": "Immettere il successo della cassetta postale", "database_connection_closed": "Connessione del database chiuso", "browser_error": "Errore di controllo del browser: {errore}", "select_email_domain_success": "Seleziona il successo del dominio e -mail", "database_updated_successfully": "Database aggiornato correttamente", "generate_email": "Generare una nuova e -mail", "email_copy_error": "Errore di copia e -mail: {errore}", "save_token_failed": "Salva token fallito", "navigation_error": "Errore di navigazione: {errore}", "get_cursor_session_token_failed": "Ottieni il token di sessione del cursore fallito", "check_verification": "Controllo del codice di verifica", "mailbox_error": "Errore della cassetta postale: {errore}", "get_cursor_session_token": "Ottieni il token della sessione del cursore", "enter_mailbox": "Entrando in cassetta postale"}, "config": {"config_updated": "Configurazione aggiornata", "configuration": "Configurazione", "file_owner": "Proprietario del file: {proprietario}", "error_checking_linux_paths": "Errore che controlla i percorsi Linux: {errore}", "storage_file_is_empty": "Il file di archiviazione è vuoto: {Storage_path}", "config_directory": "Directory di configurazione", "documents_path_not_found": "Percorso dei documenti non trovati, usando la directory corrente", "config_not_available": "Configurazione non disponibile", "neither_cursor_nor_cursor_directory_found": "Né la directory del cursore né del cursore trovato in {config_base}", "please_make_sure_cursor_is_installed_and_has_been_run_at_least_once": "Assicurati che il cursore sia installato ed è stato eseguito almeno una volta", "config_created": "Config create: {config_file}", "using_temp_dir": "Utilizzando la directory temporanea a causa di errore: {path} (errore: {errore})", "storage_file_not_found": "File di archiviazione non trovato: {Storage_path}", "the_file_might_be_corrupted_please_reinstall_cursor": "Il file potrebbe essere corrotto, si prega di reinstallare il cursore", "error_getting_file_stats": "Errore per ottenere statistiche dei file: {errore}", "enabled": "Abilitato", "backup_created": "Backup Creato: {Path}", "file_permissions": "Autorizzazioni di file: {autorizzazioni}", "config_setup_error": "Errore Impostazione di configurazione: {errore}", "config_force_update_enabled": "Aggiornamento della forza del file di configurazione abilitato, eseguendo l'aggiornamento forzato", "config_removed": "File di configurazione rimosso per l'aggiornamento forzato", "file_size": "Dimensione del file: {size} byte", "error_reading_storage_file": "Errore il file di archiviazione di lettura: {errore}", "config_force_update_disabled": "Aggiornamento della forza del file di configurazione disabilitato, saltando l'aggiornamento forzato", "config_dir_created": "Directory config create: {path}", "config_option_added": "Opzione di configurazione aggiunta: {opzione}", "file_group": "File Group: {Group}", "and": "E", "backup_failed": "Impossibile backup Config: {Errore}", "force_update_failed": "Forza aggiornamento config non riuscita: {errore}", "storage_directory_not_found": "Directory di archiviazione non trovata: {Storage_dir}", "also_checked": "Anche controllato {Path}", "try_running": "Prova a eseguire: {comando}", "storage_file_found": "File di archiviazione trovato: {Storage_path}", "disabled": "Disabile", "storage_file_is_valid_and_contains_data": "Il file di archiviazione è valido e contiene dati", "permission_denied": "Autorizzazione negata: {Storage_path}"}, "bypass": {"found_product_json": "Trovato prodotto.json: {path}", "starting": "Bypass della versione del cursore iniziale ...", "version_updated": "Versione aggiornata da {old} a {new}", "menu_option": "Controllo della versione del cursore di bypass", "unsupported_os": "Sistema operativo non supportato: {System}", "backup_created": "Backup Creato: {Path}", "current_version": "Versione corrente: {versione}", "localappdata_not_found": "LocalAppdata Environment Variable non trovata", "no_write_permission": "Nessuna autorizzazione di scrittura per file: {path}", "write_failed": "Impossibile scrivere Product.json: {Errore}", "description": "Questo strumento modifica il prodotto del cursore.json per bypass le restrizioni della versione", "bypass_failed": "Bypass della versione non riuscita: {errore}", "title": "Strumento di bypass della versione del cursore", "no_update_needed": "Nessun aggiornamento necessario. La versione corrente {versione} è già> = 0.46.0", "read_failed": "Impossibile leggere Product.json: {errore}", "stack_trace": "Traccia dello stack", "product_json_not_found": "Product.json non trovato nei percorsi comuni di Linux", "file_not_found": "File non trovato: {path}"}, "auth": {"press_enter": "Premere Invio per uscire", "auth_update_failed": "Aggiornamento delle informazioni di autentica non riuscita: {errore}", "auth_file_error": "Errore del file di auth: {errore}", "checking_auth": "Controllo del file di auth", "title": "<PERSON><PERSON><PERSON>", "connected_to_database": "Connesso al database", "db_not_found": "File di database non trovato su: {Path}", "db_permission_error": "Impossibile accedere al file di database. Si prega di controllare le autorizzazioni", "updating_pair": "Aggiornamento della coppia di valore chiave", "reading_auth": "Leggendo il file di autenticazione", "auth_updated": "Informazioni di autenticazione aggiornate correttamente", "auth_not_found": "File di autentica non trovato", "updating_auth": "Aggiornamento delle informazioni sull'auth", "db_connection_error": "Impossibile connettersi al database: {errore}", "auth_file_create_failed": "File di auth crea non riuscita: {errore}", "database_updated_successfully": "Database aggiornato correttamente", "reset_machine_id": "Ripristina ID macchina", "database_connection_closed": "Connessione del database chiuso", "auth_file_created": "File di auth creato"}, "bypass_token_limit": {"description": "Questo strumento modifica il file workbench.desktop.main.js per bypassare il limite token", "press_enter": "Premere Invio per continuare ...", "title": "Strumento di limite di bypass token"}, "tempmail": {"config_error": "Errore del file di configurazione: {errore}", "no_email": "Nessuna e -mail di verifica del cursore trovato", "general_error": "Si è verificato un errore: {errore}", "extract_code_failed": "Extract Verifica Codice non riuscito: {errore}", "configured_email": "Email configurata: {email}", "no_code": "Impossibile ottenere il codice di verifica", "checking_email": "Verificare la verifica della verifica del cursore ...", "check_email_failed": "Controlla l'e -mail non riuscita: {errore}", "email_found": "Email di verifica del cursore trovato", "verification_code": "Codice di verifica: {codice}"}}