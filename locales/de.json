{"menu": {"title": "Verfügbare Optionen", "exit": "<PERSON><PERSON>", "reset": "Maschinen-ID <PERSON>", "register": "Neues Cursor-Konto Registrieren", "register_google": "Mit Google-Konto Registrieren", "register_github": "<PERSON><PERSON>-Konto Registrieren", "register_manual": "Cursor <PERSON>t <PERSON>utzerdefinierter E-Mail Registrieren", "quit": "Cursor-Anwendung Schließen", "select_language": "Sprache Ändern", "select_chrome_profile": "Chrome-<PERSON><PERSON>", "input_choice": "<PERSON>te geben Si<PERSON> Ihre Auswahl ein ({choices})", "invalid_choice": "Ungültige Auswahl. Bitte eine Nummer von {choices} eingeben", "program_terminated": "<PERSON>m wurde vom Benutzer beendet", "error_occurred": "Ein Fehler ist aufgetreten: {error}. Bitte erneut versuchen", "press_enter": "<PERSON><PERSON><PERSON> Sie Enter zum Beenden", "disable_auto_update": "Cursor Auto-Update Deaktivieren", "lifetime_access_enabled": "LEBENSLANGER ZUGRIFF AKTIVIERT", "totally_reset": "Cursor Vollständig Zurücksetzen", "outdate": "<PERSON><PERSON><PERSON>", "temp_github_register": "Temporäre GitHub-Registrierung", "admin_required": "Ausführen als ausf<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>.", "admin_required_continue": "Mit der aktuellen Version fortfahren...", "coming_soon": "<PERSON>ld verfügbar", "fixed_soon": "<PERSON>ld <PERSON>", "contribute": "Zum Projekt Beitragen", "config": "Konfiguration Anzeigen", "delete_google_account": "Cursor Google-Konto <PERSON>", "continue_prompt": "Fortfahren? (y/N): ", "operation_cancelled_by_user": "Vorgang vom Benutzer abgebrochen", "exiting": "Wird beendet ……", "bypass_version_check": "Cursor Versionsprüfung Überspringen", "check_user_authorized": "Benutzerautorisierung Prüfen", "bypass_token_limit": "Token-<PERSON>it um<PERSON>hen", "restore_machine_id": "Geräte-ID aus Backup wiederherstellen", "language_config_saved": "Sprachkonfiguration erfolgreich gespeichert", "lang_invalid_choice": "Ungültige Auswahl. Bitte geben Si<PERSON> eine der folgenden Optionen ein: ({Lang_Choices})", "manual_custom_auth": "Handbuch benutzerdefinierte Auth"}, "languages": {"ar": "Arabisch", "en": "<PERSON><PERSON><PERSON>", "zh_cn": "Vereinfachtes Chinesisch", "zh_tw": "Traditionelles Chinesisch", "vi": "Vietnamesisch", "nl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "de": "De<PERSON>ch", "fr": "Franzö<PERSON><PERSON>", "pt": "Portugiesisch", "ru": "<PERSON><PERSON>", "es": "Spanisch", "tr": "Türkisch", "bg": "Bulgarisch", "ja": "japanisch", "it": "Italienisch"}, "quit_cursor": {"start": "<PERSON><PERSON><PERSON>", "no_process": "<PERSON><PERSON>ursor-Prozess", "terminating": "Been<PERSON> Prozess {pid}", "waiting": "Warte auf Prozessende", "success": "Alle Cursor-<PERSON><PERSON><PERSON>", "timeout": "Prozess-Timeout: {pids}", "error": "<PERSON>hler Aufgetreten: {error}"}, "reset": {"title": "<PERSON><PERSON><PERSON>-ID Zurücksetzen Tool", "checking": "Konfigurationsdatei Überprüfen", "not_found": "Konfigurationsdatei Nicht Gefunden", "no_permission": "<PERSON>nn Konfigurationsdatei Nicht Lesen oder Schreiben, Bitte Dateiberechtigungen Überprüfen", "reading": "Aktuelle Konfiguration Lesen", "creating_backup": "Konfigurations-<PERSON><PERSON>", "backup_exists": "Backup-<PERSON><PERSON>, Backup-Schritt Überspringen", "generating": "<PERSON><PERSON><PERSON> Ma<PERSON>inen-ID Generieren", "saving_json": "Neue Konfiguration in JSON Speichern", "success": "Maschinen-ID Erfolgreich Zurückgesetzt", "new_id": "Neue Maschinen-ID", "permission_error": "Berechtigungsfehler: {error}", "run_as_admin": "Bitte Versuchen Sie, <PERSON><PERSON> Programm als Administrator Auszuführen", "process_error": "Zurücksetzungsprozessfehler: {error}", "updating_sqlite": "SQLite-Datenbank Aktualisieren", "updating_pair": "Schlüssel-Wert-Paar Aktualisieren", "sqlite_success": "SQLite-Datenbank Erfolgreich Aktualisiert", "sqlite_error": "SQLite-Datenbank Aktualisierung Fehlgeschlagen: {error}", "press_enter": "Drücken Sie Enter zum Fortfahren", "unsupported_os": "Nicht Unterstütztes Betriebssystem: {os}", "linux_path_not_found": "Linux-Pfad Nicht Gefunden", "updating_system_ids": "System-IDs Aktualisieren", "system_ids_updated": "System-IDs Erfolgreich Aktualisiert", "system_ids_update_failed": "System-IDs Aktualisierung Fehlgeschlagen: {error}", "windows_guid_updated": "Windows GUID Erfolgreich Aktualisiert", "windows_permission_denied": "Windows Berechtigung Verweigert", "windows_guid_update_failed": "Windows GUID Aktualisierung Fehlgeschlagen", "macos_uuid_updated": "macOS UUID Erfolgreich Aktualisiert", "plutil_command_failed": "plutil-Befehl Fehlgeschlagen", "start_patching": "Patching getMachineId Starten", "macos_uuid_update_failed": "macOS UUID Aktualisierung Fehlgeschlagen", "current_version": "Aktuelle Cursor-Version: {version}", "patch_completed": "Patching getMachineId Abgeschlossen", "patch_failed": "Patching getMachineId Fehlgeschlagen: {error}", "version_check_passed": "Cursor-Version Überprüfung Erfolgreich", "file_modified": "<PERSON><PERSON>", "version_less_than_0_45": "Cursor-Version < 0.45.0, Patching getMachineId Überspringen", "detecting_version": "Cursor-Version Erkennen", "patching_getmachineid": "Patching getMachineId", "version_greater_than_0_45": "Cursor-Version >= 0.45.0, Patching getMachineId", "permission_denied": "Berechtigung Verweigert: {error}", "backup_created": "<PERSON><PERSON>", "update_success": "Update Erfolgreich", "update_failed": "Update Fehlgeschlagen: {error}", "windows_machine_guid_updated": "Windows Maschinen-GUID Erfolgreich Aktualisiert", "reading_package_json": "package.j<PERSON> {path}", "invalid_json_object": "Ungültiges JSON-Objekt", "no_version_field": "<PERSON><PERSON> in package.json Gefunden", "version_field_empty": "Versionsfeld ist Leer", "invalid_version_format": "Ungültiges Versionsformat: {version}", "found_version": "Gefundene Version: {version}", "version_parse_error": "Versions-Parse-<PERSON><PERSON>: {error}", "package_not_found": "Package.json Nicht Gefunden: {path}", "check_version_failed": "Versionsüberprüfung Fehlgeschlagen: {error}", "stack_trace": "Stack Trace", "version_too_low": "Cursor-Version Zu Niedrig: {version} < 0.45.0", "no_write_permission": "<PERSON><PERSON>ei<PERSON>ig<PERSON>: {path}", "path_not_found": "Pfad Nicht Gefunden: {path}", "modify_file_failed": "Datei Ändern Fehlgeschlagen: {error}", "windows_machine_id_updated": "Windows Maschinen-ID Erfolgreich Aktualisiert", "update_windows_machine_id_failed": "Windows Maschinen-ID Aktualisierung Fehlgeschlagen: {error}", "update_windows_machine_guid_failed": "Windows Maschinen-GUID Aktualisierung Fehlgeschlagen: {error}", "file_not_found": "Datei Nicht Gefunden: {path}"}, "register": {"title": "Cursor Registrierungstool", "start": "Registrierungsprozess Starten...", "handling_turnstile": "Sicherheitsüberprüfung Verarbeiten...", "retry_verification": "Überprüfung Erneut Versuchen...", "detect_turnstile": "Sicherheitsüberprüfung Überprüfen...", "verification_success": "Sicherheitsüberprüfung Erfolgreich", "starting_browser": "Browser <PERSON>...", "form_success": "Formular Erfolg<PERSON><PERSON> Eingereicht", "browser_started": "<PERSON><PERSON><PERSON> Erfolg<PERSON><PERSON>", "waiting_for_second_verification": "Warten auf E-Mail-Verifizierung...", "waiting_for_verification_code": "Warten auf Verifizierungscode...", "password_success": "Passwort Erfolgreich Eingestellt", "password_error": "Konnte Passwort Nicht Einstellen: {error}. Bitte Erneut Versuchen", "waiting_for_page_load": "Seite Laden...", "first_verification_passed": "<PERSON>rste Überprüfung Erfolgreich", "mailbox": "E-Mail-Postfach Erfolgreich Geöffnet", "register_start": "Registrierung Starten", "form_submitted": "Formular Eingereicht, Überprüfung Starten...", "filling_form": "<PERSON><PERSON>", "visiting_url": "URL Besuchen", "basic_info": "Grundlegende Informationen Eingereicht", "handle_turnstile": "<PERSON><PERSON><PERSON>", "no_turnstile": "<PERSON><PERSON>", "turnstile_passed": "<PERSON><PERSON><PERSON>ich", "verification_start": "Verifizierungscode Erhalten Starten", "verification_timeout": "Verifizierungscode Timeout", "verification_not_found": "<PERSON><PERSON> Verifizierungscode Gefunden", "try_get_code": "Versuchen | {attempt} Verifizierungscode Erhalten | Verbleibende Zeit: {time}s", "get_account": "Kontoinformationen Erhalten", "get_token": "Cursor-Sitzungstoken Erhalten", "token_success": "Token Erfolgreich Erhalten", "token_attempt": "Versuchen | {attempt} <PERSON> Token zu Erhalten | <PERSON><PERSON><PERSON> V<PERSON> in {time}s", "token_max_attempts": "Maximale Versuche Erreicht ({max}) | Token Erhalten Fehlgeschlagen", "token_failed": "Token Erhalten Fehlgeschlagen: {error}", "account_error": "Kontoinformationen Erhalten Fehlgeschlagen: {error}", "press_enter": "Drücken Sie Enter zum Fortfahren", "browser_start": "<PERSON><PERSON><PERSON>en", "open_mailbox": "Mailbox-Se<PERSON>", "email_error": "E-Mail-Adresse Erhalten Fehlgeschlagen", "setup_error": "E-Mail-Einrichtungsfehler: {error}", "start_getting_verification_code": "Verifizierungscode Erhalten Starten, Erneut Versuchen in 60s", "get_verification_code_timeout": "Verifizierungscode Erhalten Timeout", "get_verification_code_success": "Verifizierungscode Erfolgreich Erhalten", "try_get_verification_code": "Versuchen | {attempt} Verifizierungscode Erhalten | Verbleibende Zeit: {remaining_time}s", "verification_code_filled": "Verifizierungscode Ausgefüllt", "login_success_and_jump_to_settings_page": "Anmeldung Erfolgreich und zu Einstellungsseite Springen", "detect_login_page": "Anmeldeseite Erkennen, Anmeldung Starten...", "cursor_registration_completed": "Cursor-Registrierung Abgeschlossen!", "set_password": "Passwort Einstellen", "basic_info_submitted": "Grundlegende Informationen Eingereicht", "cursor_auth_info_updated": "Cursor-Authentifizierungsinformationen Aktualisiert", "cursor_auth_info_update_failed": "Cursor-Authentifizierungsinformationen Aktualisierung Fehlgeschlagen", "reset_machine_id": "Maschinen-ID <PERSON>", "account_info_saved": "Kontoinformationen Gespeichert", "save_account_info_failed": "Kontoinformationen Speichern Fehlgeschlagen", "get_email_address": "E-Mail-Adress<PERSON>", "update_cursor_auth_info": "Cursor-Authentifizierungsinformationen Aktualisieren", "register_process_error": "Registrierungsprozessfehler: {error}", "setting_password": "Passwort Einstellen", "manual_code_input": "Manuelle Code-Eingabe", "manual_email_input": "Manuelle E-Mail-Eingabe", "password": "Passwort", "first_name": "<PERSON><PERSON><PERSON>", "last_name": "Nachname", "exit_signal": "Exit-Signal", "email_address": "E-Mail-Adresse", "config_created": "Konfiguration Erstellt", "verification_failed": "Verifizierung Fehlgeschlagen", "verification_error": "Verifizierungsfehler: {error}", "config_option_added": "Konfigurationsoption Hinzugefügt: {option}", "config_updated": "Konfiguration Aktualisiert", "password_submitted": "Passwort Eingereicht", "total_usage": "Gesamtnutzung: {usage}", "setting_on_password": "Passwort Einstellen", "getting_code": "Verifizierungscode Erhalten, Erneut Versuchen in 60s", "using_browser": "<PERSON><PERSON><PERSON><PERSON> Sie {<PERSON>rows<PERSON>} Browser: {Path}", "could_not_track_processes": "Konnte {<PERSON><PERSON><PERSON>} Prozesse nicht verfolgen: {<PERSON><PERSON>}", "try_install_browser": "Installieren Sie den Browser mit Ihrem Paketmanager", "tempmail_plus_verification_started": "TempmailPlus -Überprüfungsprozess starten", "max_retries_reached": "Maximale Wiederholungsversuche erreichten. Registrierung fehlgeschlagen.", "tempmail_plus_enabled": "TempmailPlus ist aktiviert", "browser_path_invalid": "{Browser} Pfad ist ungültig unter Verwendung des Standardpfads", "human_verify_error": "Ich kann nicht überprüfen, ob der Benutzer menschlich ist. Wiederholung ...", "using_tempmail_plus": "<PERSON><PERSON><PERSON><PERSON> von TempmailPlus zur E -Mail -Überprüfung", "tracking_processes": "Tracking {count} {<PERSON><PERSON><PERSON>} Prozesse", "tempmail_plus_verification_failed": "TEMPMAILPLUS -Überprüfung fehlgeschlagen: {<PERSON><PERSON>}", "tempmail_plus_epin_missing": "TempmailPlus Epin ist nicht konfiguriert", "using_browser_profile": "<PERSON><PERSON><PERSON><PERSON> {<PERSON><PERSON><PERSON>} <PERSON><PERSON> von: {user_data_dir}", "tempmail_plus_verification_completed": "TEMPMAILPLUS -Überprüfung erfolgreich abgeschlossen", "tempmail_plus_email_missing": "TempmailPlus -E -Mail ist nicht konfiguriert", "tempmail_plus_init_failed": "Tempmailplus nicht initialisieren: {<PERSON><PERSON>}", "tempmail_plus_config_missing": "Die Konfiguration von TempmailPlus fehlt", "tempmail_plus_initialized": "TempmailPlus erfolgreich initialisiert", "tempmail_plus_disabled": "TempmailPlus ist deaktiviert", "no_new_processes_detected": "<PERSON><PERSON> neuen {<PERSON><PERSON><PERSON>} -Prozesse zur Verfolgung erkannt", "make_sure_browser_is_properly_installed": "<PERSON><PERSON><PERSON>, dass {<PERSON><PERSON><PERSON>} ordnungsgemäß installiert ist"}, "auth": {"title": "<PERSON><PERSON><PERSON>ung<PERSON>", "checking_auth": "Authentifizierungsdatei Überprüfen", "auth_not_found": "Authentifizierungsdatei Nicht Gefunden", "auth_file_error": "Authentifizierungsdateifehler: {error}", "reading_auth": "Authentifizierungsdatei Lesen", "updating_auth": "Authentifizierungsinformationen Aktualisieren", "auth_updated": "Authentifizierungsinformationen Erfolgreich Aktualisiert", "auth_update_failed": "Authentifizierungsinformationen Aktualisierung Fehlgeschlagen: {error}", "auth_file_created": "Authentifizierungsdatei Erstellt", "auth_file_create_failed": "Authentifizierungsdatei Erstellen Fehlgeschlagen: {error}", "press_enter": "Drücken Sie Enter zum Fortfahren", "reset_machine_id": "Maschinen-ID <PERSON>", "database_connection_closed": "Datenbankverbindung Geschlossen", "database_updated_successfully": "Datenbank Erfolgreich Aktualisiert", "connected_to_database": "Mit Datenbank Verbunden", "updating_pair": "Schlüssel-Wert-Paar Aktualisieren", "db_not_found": "Datenbankdatei Nicht Gefunden bei: {path}", "db_permission_error": "Kann Nicht auf Datenbankdatei Zugreifen. Bitte Berechtigungen Überprüfen", "db_connection_error": "Verbindung zur Datenbank Fehlgeschlagen: {error}"}, "control": {"generate_email": "Neue E-Mail Generieren", "blocked_domain": "Gesperrte Domain", "select_domain": "Zufällige Domain Auswählen", "copy_email": "E-Mail-<PERSON><PERSON><PERSON>", "enter_mailbox": "Mailbox Betreten", "refresh_mailbox": "Mailbox Aktualisieren", "check_verification": "Verifizierungscode Überprüfen", "verification_found": "Verifizierungscode Gefunden", "verification_not_found": "<PERSON><PERSON> Verifizierungscode Gefunden", "browser_error": "Browsersteuerungsfehler: {error}", "navigation_error": "Navigationsfehler: {error}", "email_copy_error": "E-Mail-Kopierf<PERSON>ler: {error}", "mailbox_error": "Mailbox-Fehler: {error}", "token_saved_to_file": "Token Gespeichert in cursor_tokens.txt", "navigate_to": "Navigieren zu {url}", "generate_email_success": "E-Mail Erfolgreich Generiert", "select_email_domain": "E-Mail-Domain Auswählen", "select_email_domain_success": "E-Mail-Domain Erfolgreich Ausgewählt", "get_email_name": "E-Mail-Name <PERSON>halt<PERSON>", "get_email_name_success": "E-Mail-Name Erfolgreich Erhalten", "get_email_address": "E-Mail-Adress<PERSON>", "get_email_address_success": "E-Mail-Adresse Erfolgreich Erhalten", "enter_mailbox_success": "Mailbox Erfolgreich Betreten", "found_verification_code": "Verifizierungscode Gefunden", "get_cursor_session_token": "Cursor-Sitzungstoken Erhalten", "get_cursor_session_token_success": "Cursor-Sitzungstoken Erfolgreich Erhalten", "get_cursor_session_token_failed": "Cursor-Sitzungstoken Erhalten Fehlgeschlagen", "save_token_failed": "Token Speichern Fehlgeschlagen", "database_updated_successfully": "Datenbank Erfolgreich Aktualisiert", "database_connection_closed": "Datenbankverbindung Geschlossen", "no_valid_verification_code": "<PERSON><PERSON> Verifizierungscode"}, "email": {"starting_browser": "<PERSON><PERSON><PERSON>en", "visiting_site": "Besuche mail domains", "create_success": "E-Mail Erfolgreich Erstellt", "create_failed": "E-Mail Erstellen Fehlgeschlagen", "create_error": "E-Mail-Erstellungsfehler: {error}", "refreshing": "E-Mail Aktualisieren", "refresh_success": "E-Mail Erfolgreich Aktualisiert", "refresh_error": "E-Mail-Aktualisierungsfehler: {error}", "refresh_button_not_found": "Aktualisierungsknopf Nicht Gefunden", "verification_found": "Verifizierung Gefunden", "verification_not_found": "Verifizierung Nicht Gefunden", "verification_error": "Verifizierungsfehler: {error}", "verification_code_found": "Verifizierungscode Gefunden", "verification_code_not_found": "Verifizierungscode Nicht Gefunden", "verification_code_error": "Verifizierungscodefehler: {error}", "address": "E-Mail-Adresse", "all_domains_blocked": "Alle Domains Gesperrt, Service Wechseln", "no_available_domains_after_filtering": "<PERSON>ine Verfügbaren Domains Nach Filterung", "switching_service": "Wechseln zu {service} Service", "domains_list_error": "Domains-Liste Erhalten Fehlgeschlagen: {error}", "failed_to_get_available_domains": "Verfügbare Domains Erhalten Fehlgeschlagen", "domains_excluded": "Ausgeschlossene Domains: {domains}", "failed_to_create_account": "Konto Erstellen Fehlgeschlagen", "account_creation_error": "Konto-Erstellungsfehler: {error}", "domain_blocked": "Domain Blocked: {domain}", "no_display_found": "<PERSON><PERSON> Display gefunden. <PERSON><PERSON><PERSON>, dass der X -Server ausgeführt wird.", "try_export_display": "Versuchen Sie: Exportanzeige =: 0", "try_install_chromium": "Versuchen Sie: sudo apt installieren Chrom-Browser", "blocked_domains": "Blockierte Domänen: {Domains}", "blocked_domains_loaded_timeout_error": "Blockierte Domänen geladener Zeitüberschreitungsfehler: {<PERSON><PERSON>}", "blocked_domains_loaded_success": "Blockierte Domänen erfolgreich geladen", "extension_load_error": "Erweiterungslastfehler: {<PERSON><PERSON>}", "available_domains_loaded": "Verfügbare Domänen geladen: {count}", "blocked_domains_loaded_error": "Blockierte Domänen geladener <PERSON>hler: {<PERSON><PERSON>}", "blocked_domains_loaded_timeout": "<PERSON>ierte Domänen geladen Timeout: {Timeout} s", "make_sure_chrome_chromium_is_properly_installed": "<PERSON><PERSON><PERSON>, dass Chrom/Chrom ordnungsgemäß installiert ist", "domains_filtered": "<PERSON><PERSON><PERSON> gefiltert: {count}", "trying_to_create_email": "<PERSON><PERSON><PERSON>, E -Mail zu erstellen: {E -Mail}", "using_chrome_profile": "Verwenden Sie das Chrome -Profil von: {user_data_dir}", "blocked_domains_loaded": "<PERSON>ierte Domänen geladen: {count}"}, "update": {"title": "Cursor Auto-Update Deaktivieren", "disable_success": "Auto-Update Deaktiviert Erfolgreich", "disable_failed": "Auto-Update Deaktivieren Fehlgeschlagen: {error}", "press_enter": "Drücken Sie Enter zum Fortfahren", "start_disable": "Auto-Update Deaktivieren Starten", "killing_processes": "<PERSON><PERSON><PERSON>", "processes_killed": "Prozesse <PERSON>ö<PERSON>t", "removing_directory": "Verzeichnis Entfernen", "directory_removed": "Verzeichnis Entfernt", "creating_block_file": "Block-<PERSON><PERSON>", "block_file_created": "Block-<PERSON><PERSON>", "clearing_update_yml": "Clearing update.yml -Datei", "update_yml_cleared": "update.yml -<PERSON><PERSON>", "unsupported_os": "Nicht unterstütztes Betriebssystem: {System}", "block_file_already_locked": "Die Blockdatei ist bereits gesperrt", "yml_already_locked_error": "update.yml -Datei bereits ges<PERSON><PERSON>: {<PERSON><PERSON>}", "update_yml_not_found": "update.yml -Datei nicht gefunden", "yml_locked_error": "update.yml -<PERSON><PERSON> g<PERSON><PERSON>: {<PERSON><PERSON>}", "remove_directory_failed": "Verzeichnis nicht entfernen: {<PERSON><PERSON>}", "create_block_file_failed": "Die Blockdatei nicht erstellt: {<PERSON><PERSON>}", "yml_already_locked": "update.yml -Datei ist bereits gesperrt", "block_file_locked_error": "<PERSON><PERSON><PERSON> g<PERSON><PERSON><PERSON>: {<PERSON><PERSON>}", "directory_locked": "Verzeichnis ist gesperrt: {Path}", "block_file_already_locked_error": "Blockdatei bereits ges<PERSON><PERSON>: {<PERSON><PERSON>}", "clear_update_yml_failed": "<PERSON><PERSON>geschlagen, update.yml -Datei zu löschen: {<PERSON><PERSON>}", "yml_locked": "update.yml -Datei ist gesperrt", "block_file_locked": "Die Blockdatei ist gesperrt"}, "updater": {"checking": "Updates prüfen...", "new_version_available": "Neue Version verfügbar! (Aktuell: {current}, Neueste: {latest})", "updating": "Zur neuesten Version aktualisieren. Das Programm wird automatisch neu starten.", "up_to_date": "Sie verwenden die neueste Version.", "check_failed": "Überprüfung auf Updates fehlgeschlagen: {error}", "continue_anyway": "Mit der aktuellen Version fortfahren...", "update_confirm": "Möchten Sie die neueste Version aktualisieren? (Y/n)", "update_skipped": "Update überspringen.", "invalid_choice": "Ungültige Auswahl. Bitte geben Sie 'Y' oder 'n' ein.", "development_version": "Entwickler-Version {current} > {latest}", "changelog_title": "Changelog", "rate_limit_exceeded": "Die Github -API -Ratengrenze überschritten. Überspringen Sie Update -Check."}, "totally_reset": {"title": "Cursor Vollständig Zurücksetzen", "checking_config": "Konfigurationsdatei Überprüfen", "config_not_found": "Konfigurationsdatei Nicht Gefunden", "no_permission": "<PERSON>nn Konfigurationsdatei Nicht Lesen oder Schreiben, Bitte Berechtigungen Überprüfen", "reading_config": "Aktuelle Konfiguration Lesen", "creating_backup": "Konfigurationsdatei <PERSON>", "backup_exists": "Backup-Datei bereits vorhanden, Sicherungsschritt überspringen", "generating_new_machine_id": "<PERSON><PERSON><PERSON> Ma<PERSON>inen-ID Generieren", "saving_new_config": "Neue Konfiguration in JSON Speichern", "success": "Cursor Erfolgreich Zurückgesetzt", "error": "<PERSON><PERSON><PERSON>zen Fehlgeschlagen: {error}", "press_enter": "<PERSON><PERSON><PERSON> Sie Enter zum Beenden", "reset_machine_id": "Maschinen-ID <PERSON>", "database_connection_closed": "Datenbankverbindung Geschlossen", "database_updated_successfully": "Datenbank Erfolgreich Aktualisiert", "connected_to_database": "Mit Datenbank Verbunden", "updating_pair": "Schlüssel-Wert-Paar Aktualisieren", "db_not_found": "Datenbankdatei Nicht Gefunden bei: {path}", "db_permission_error": "Kann Nicht auf Datenbankdatei Zugreifen. Bitte Berechtigungen Überprüfen", "db_connection_error": "Verbindung zur Datenbank Fehlgeschlagen: {error}", "feature_title": "FEATURES", "feature_1": "Vollständige Entfernung von Cursor AI Einstellungen und Konfigurationen", "feature_2": "Entfernt alle zwischengespeicherten Daten, einschließlich AI-Verlauf und Prompts", "feature_3": "Maschinen-<PERSON>, um Trial-Erkennung zu umgehen", "feature_4": "Erstellt neue zufällige Maschinen-IDs", "feature_5": "Entfernt benutzerdefinierte Erweiterungen und Einstellungen", "feature_6": "Zurücksetzt Trial-Informationen und Aktivierungsdaten", "feature_7": "Tiefes Scannen für versteckte Lizenz- und Trial-bezogene Dateien", "feature_8": "<PERSON><PERSON><PERSON> nicht-Cursor-Dateien und Anwendungen", "feature_9": "Kompatibel mit Windows, macOS und Linux", "disclaimer_title": "Disclaimer", "disclaimer_1": "<PERSON><PERSON> wird alle Cursor AI Einstellungen,", "disclaimer_2": "Konfigurationen und zwischengespeicherte Daten löschen. Diese Aktion kann nicht rückgängig gemacht werden.", "disclaimer_3": "Ihre Code-<PERSON><PERSON> werden NICHT beeinflusst, und das Tool ist so konzipiert,", "disclaimer_4": "nur Cursor AI Editor-<PERSON><PERSON> und Trial-Erkennungsmechanismen zu zielen.", "disclaimer_5": "Andere Anwendungen auf Ihrem System werden NICHT beeinflusst.", "disclaimer_6": "<PERSON>e müssen Cursor AI erneut ein<PERSON>, nachdem Si<PERSON> dieses Tool ausgeführt haben.", "disclaimer_7": "Verwenden Sie auf eigene Gefahr", "confirm_title": "Sind Si<PERSON> sicher, dass Sie fortfahren möchten?", "confirm_1": "Diese Aktion wird alle Cursor AI Einstellungen,", "confirm_2": "Konfigurationen und zwischengespeicherte Daten löschen. Diese Aktion kann nicht rückgängig gemacht werden.", "confirm_3": "Ihre Code-<PERSON><PERSON> werden NICHT beeinflusst, und das Tool ist so konzipiert,", "confirm_4": "nur Cursor AI Editor-<PERSON><PERSON> und Trial-Erkennungsmechanismen zu zielen.", "confirm_5": "Andere Anwendungen auf Ihrem System werden NICHT beeinflusst.", "confirm_6": "<PERSON>e müssen Cursor AI erneut ein<PERSON>, nachdem Si<PERSON> dieses Tool ausgeführt haben.", "confirm_7": "Verwenden Sie auf eigene Gefahr", "invalid_choice": "Bitte geben Sie 'Y' oder 'n' ein", "skipped_for_safety": "Übersprungen für Sicherheit (nicht Cursor-bezogen): {path}", "deleted": "Gelöscht: {path}", "error_deleting": "<PERSON><PERSON> beim Löschen von {path}: {error}", "not_found": "Datei nicht gefunden: {path}", "resetting_machine_id": "Ma<PERSON>inen-<PERSON><PERSON> zurück<PERSON>zen, um Trial-Erkennung zu umgehen...", "created_machine_id": "<PERSON>eue Maschinen-ID erstellt: {path}", "error_creating_machine_id": "<PERSON><PERSON> beim Erstellen der Maschinen-ID-Datei {path}: {error}", "error_searching": "<PERSON><PERSON> beim Suchen nach Dateien in {path}: {error}", "created_extended_trial_info": "Neue erweiterte Trial-Informationen erstellt: {path}", "error_creating_trial_info": "<PERSON><PERSON> beim Erstellen der Trial-Informationen-Datei {path}: {error}", "resetting_cursor_ai_editor": "Cursor AI Editor zurücksetzen... Bitte warten.", "reset_cancelled": "Reset abgebrochen. Ohne Änderungen verlassen.", "windows_machine_id_modification_skipped": "Windows Maschinen-ID-Änderung übersprungen: {error}", "linux_machine_id_modification_skipped": "Linux machine-id-Änderung übersprungen: {error}", "note_complete_machine_id_reset_may_require_running_as_administrator": "Hinweis: Vollständiges Zurücksetzen der Maschinen-ID kann er<PERSON>, dass Sie als Administrator ausführen", "note_complete_system_machine_id_reset_may_require_sudo_privileges": "Hinweis: Vollständiges System-Maschinen-ID-Zurücksetzen kann sudo-Berechtigungen erfordern", "windows_registry_instructions": "📝 HINWEIS: F<PERSON>r vollständiges Zurücksetzen auf Windows müssen Sie möglicherweise auch die Registrierungseinträge bereinigen.", "windows_registry_instructions_2": "   <PERSON>ühren Sie 'regedit' aus und suchen Si<PERSON> nach Schlüsseln, die 'Cursor' oder 'CursorAI' enthalten, unter HKEY_CURRENT_USER\\Software\\ und löschen Sie sie.\n", "reset_log_1": "Cursor AI wurde vollständig zurückgesetzt und Trial-Erkennung umgangen!", "reset_log_2": "Bitte starten Sie Ihr System neu, um die Änderungen zu übernehmen.", "reset_log_3": "Sie müssen Cursor AI erneut installieren und sollten jetzt einen neuen Trial-Zeitraum haben.", "reset_log_4": "<PERSON>ür die besten Ergebnisse betrachten Sie auch:", "reset_log_5": "Verwenden Sie eine andere E-Mail-Adresse beim Registrieren für einen neuen Trial", "reset_log_6": "<PERSON><PERSON>, verwenden Sie einen VPN, um Ihre IP-Adresse zu ändern", "reset_log_7": "Löschen Sie Ihre Browser-Cookies und Cache vor dem Besuch der Cursor AI-Website", "reset_log_8": "<PERSON><PERSON>eh<PERSON>, <PERSON>rs<PERSON><PERSON>, Cursor AI in einem anderen Speicherort zu installieren", "reset_log_9": "<PERSON><PERSON> <PERSON>e irgendwelche Probleme haben, gehen Sie zu Github Issue Tracker und erstellen Sie ein Problem unter https://github.com/yeongpin/cursor-free-vip/issues", "unexpected_error": "Ein unerwarteter Fehler ist aufgetreten: {error}", "report_issue": "<PERSON><PERSON> melden <PERSON> dieses Problem bei Github Issue Tracker unter https://github.com/yeongpin/cursor-free-vip/issues", "keyboard_interrupt": "<PERSON><PERSON><PERSON> von <PERSON>er unterbrochen. Beenden...", "return_to_main_menu": "Zurück zur Hauptseite...", "process_interrupted": "Prozess un<PERSON><PERSON>. Beenden...", "press_enter_to_return_to_main_menu": "<PERSON><PERSON><PERSON>, um zur Hauptseite zurückzukehren...", "removing_known": "Bekannte Trial/Lizenz-<PERSON><PERSON> en<PERSON>", "performing_deep_scan": "Tiefes Scannen nach zusätzlichen Trial/Lizenz-Dateien", "found_additional_potential_license_trial_files": "Gefundene {count} zusätzliche potentielle Lizenz/Trial-<PERSON>ien", "checking_for_electron_localstorage_files": "Überprüfen auf Electron localStorage-Dateien", "no_additional_license_trial_files_found_in_deep_scan": "<PERSON><PERSON> zusätzlichen Lizenz/Trial-<PERSON><PERSON> in tiefem Scan gefunden", "removing_electron_localstorage_files": "Electron localStorage-<PERSON><PERSON>", "electron_localstorage_files_removed": "Electron localStorage-<PERSON><PERSON> entfernt", "electron_localstorage_files_removal_error": "<PERSON><PERSON> beim <PERSON> von Electron localStorage-Dateien: {error}", "removing_electron_localstorage_files_completed": "Entfer<PERSON> von Electron localStorage-Dateien abgeschlossen", "warning_title": "WARNUNG", "delete_input_error": "<PERSON><PERSON> finden", "direct_advanced_navigation": "Versuchen Sie die direkte Navigation auf Advanced Registerkarte", "delete_input_not_found_continuing": "Löschen Sie die Bestätigungseingabe, die nicht gefunden wurde, und versuche trotzdem weiterzumachen", "advanced_tab_not_found": "<PERSON><PERSON><PERSON><PERSON><PERSON> Register<PERSON> nicht nach mehreren Versuchen gefunden", "advanced_tab_error": "FEHLER FORDERUNG ERWEITERTE TAB: {ERROR}", "delete_input_not_found": "Bestätigungseingabe löschen, die nach mehreren Versuchen nicht gefunden wurden", "failed_to_delete_file": "Datei nicht löschen: {Pfad}", "operation_cancelled": "Operation storniert. Beenden, ohne Änderungen vorzunehmen.", "removed": "Entfernt: {Pfad}", "warning_6": "Nach dem Ausführen dieses Tools müssen Sie Cursor AI erneut einrichten.", "delete_input_retry": "Eingabe löschen nicht gefunden, versuchen {Versuch}/{max_attempts}", "warning_4": "Nur Cursor AI -Editor -Dateien und Versuchserkennungsmechanismen abzielen.", "cursor_reset_failed": "Cursor AI Editor <PERSON><PERSON> fehlgeschlagen: {<PERSON><PERSON>}", "login_redirect_failed": "Die Anmeldung umgeleitet und versuchte direkte Navigation ...", "warning_5": "Andere Anwendungen in Ihrem System sind nicht betroffen.", "failed_to_delete_file_or_directory": "<PERSON>cht löschen Datei oder Verzeichnis: {Path}", "failed_to_delete_directory": "Verzeichnis nicht löschen: {Path}", "resetting_cursor": "Cursor AI Editor zurücksetzen ... Bitte warten Sie.", "cursor_reset_completed": "Der Cursor AI -Herausgeber wurde vollständig zurückgesetzt und die Versuchserkennung umgangen!", "warning_3": "Ihre Codedateien sind nicht betroffen und das Tool ist gestaltet", "advanced_tab_retry": "Advanced Registerkarte Nicht gefunden, Versuch {Versuch}/{max_attempts}", "completed_in": "Abgeschlossen in {Zeit} Sekunden", "delete_button_retry": "Schaltfl<PERSON>che nicht gefunden, versuchen Sie {Versuch}/{max_attempts}", "advanced_tab_clicked": "Klickte auf die Registerkarte Erweitert", "already_on_settings": "Bereits auf Einstellungsseite", "found_danger_zone": "Gefahrenzonenabschnitt gefunden", "failed_to_remove": "<PERSON>cht entfernen: {Pfad}", "failed_to_reset_machine_guid": "Die Richtlinie des Maschine nicht zurücksetzen", "deep_scanning": "Tiefenscan für zusätzliche Test-/Lizenzdateien durchführen", "delete_button_clicked": "Klicken Sie auf die Schaltfläche Konto löschen", "warning_7": "Verwenden Sie auf eigenes Risiko", "delete_button_not_found": "Die Kontotaste löschen, die nach mehreren Versuchen nicht gefunden wurden", "delete_button_error": "Fehler finden Sie Schaltfläche Löschen: {<PERSON><PERSON>}", "warning_1": "Diese Aktion löscht alle Cursor -AI -Einstellungen.", "warning_2": "Konfigurationen und zwischengespeicherte Daten. Diese Aktion kann nicht rückgängig gemacht werden.", "navigating_to_settings": "Navigieren zur Seite \"Einstellungen\" ...", "cursor_reset_cancelled": "Cursor AI Editor <PERSON><PERSON>. Beenden, ohne Änderungen vorzunehmen."}, "chrome_profile": {"title": "Chrome-<PERSON><PERSON>", "select_profile": "<PERSON><PERSON><PERSON>en Sie ein Chrome-Profil zum Verwenden:", "profile_list": "Verfügbare Profile:", "default_profile": "Standard-Profil", "profile": "Profil {number}", "no_profiles": "<PERSON>ine Chrome-Profile gefunden", "error_loading": "<PERSON><PERSON> beim Laden der Chrome-Profile: {error}", "profile_selected": "Ausgewähltes Profil: {profile}", "invalid_selection": "Ungültige Auswahl. Bitte versuchen Sie es erneut", "warning_chrome_close": "Warnung: Dies wird alle laufenden Chrome-Prozes<PERSON> beenden"}, "restore": {"title": "Geräte-ID aus Backup wiederherstellen", "starting": "Starte Wiederherstellungsprozess für Geräte-ID", "no_backups_found": "<PERSON><PERSON>-<PERSON><PERSON> gefunden", "available_backups": "Verfügbare <PERSON>-<PERSON><PERSON>", "select_backup": "<PERSON>ählen Sie ein Backup zur Wiederherstellung aus", "to_cancel": "zum Abbrechen", "operation_cancelled": "Vorgang abgebrochen", "invalid_selection": "Ungültige Auswahl", "please_enter_number": "<PERSON>te geben Si<PERSON> eine gültige Zahl ein", "missing_id": "Fehlende ID: {id}", "read_backup_failed": "Backup-<PERSON><PERSON> kon<PERSON> nicht gelesen werden: {error}", "current_file_not_found": "Aktuelle Speicherdatei nicht gefunden", "current_backup_created": "Backup der aktuellen Speicherdatei erstellt", "storage_updated": "Speicherdatei erfolgreich aktualisiert", "update_failed": "Aktualisierung der Speicherdatei fehlgeschlagen: {error}", "sqlite_not_found": "SQLite-Datenbank nicht gefunden", "updating_sqlite": "Aktualisiere SQLite-Datenbank", "updating_pair": "Aktualisiere Schlüssel-Wert-Paar", "sqlite_updated": "SQLite-Datenbank erfolgreich aktualisiert", "sqlite_update_failed": "Aktualisierung der SQLite-Datenbank fehlgeschlagen: {error}", "machine_id_backup_created": "Backup der machineId-<PERSON><PERSON> er<PERSON>", "backup_creation_failed": "Backup-Erstellung fehlgeschlagen: {error}", "machine_id_updated": "machineId-Datei erfolgreich aktualisiert", "machine_id_update_failed": "Aktualisierung der machineId-Datei fehlgeschlagen: {error}", "updating_system_ids": "Aktualisiere System-IDs", "system_ids_update_failed": "Aktualisierung der System-IDs fehlgeschlagen: {error}", "permission_denied": "Zugriff verweigert. Bitte versuchen Sie es mit Administratorrechten", "windows_machine_guid_updated": "Windows-Geräte-GUID erfolgreich aktualisiert", "update_windows_machine_guid_failed": "Aktualisierung des Windows-Geräte-GUID fehlgeschlagen: {error}", "windows_machine_id_updated": "Windows-Geräte-ID erfolgreich aktualisiert", "update_windows_machine_id_failed": "Aktualisierung der Windows-Geräte-ID fehlgeschlagen: {error}", "sqm_client_key_not_found": "SQMClient-Registrierungsschlüssel nicht gefunden", "update_windows_system_ids_failed": "Aktualisierung der Windows-System-IDs fehlgeschlagen: {error}", "macos_platform_uuid_updated": "macOS-Plattform-UUID erfolgreich aktualisiert", "failed_to_execute_plutil_command": "Ausführung des plutil-Befehls fehlgeschlagen", "update_macos_system_ids_failed": "Aktualisierung der macOS-System-IDs fehlgeschlagen: {error}", "ids_to_restore": "Wiederherzustellende Geräte-IDs", "confirm": "Sind <PERSON> sicher, dass Sie diese IDs wiederherstellen möchten?", "success": "Geräte-ID erfolgreich wiederhergestellt", "process_error": "<PERSON><PERSON> beim W<PERSON>herstellungsprozess: {error}", "press_enter": "Drücken Sie Enter, um fortzufahren"}, "oauth": {"no_chrome_profiles_found": "<PERSON><PERSON> Chromprofile mit Standardeinstellung gefunden", "starting_new_authentication_process": "Neuen Authentifizierungsprozess beginnen ...", "failed_to_delete_account": "Das Konto nicht löschen: {<PERSON><PERSON>}", "found_email": "E -Mail gefunden: {E -Mail}", "github_start": "<PERSON><PERSON><PERSON> Start", "already_on_settings_page": "Bereits auf Einstellungsseite!", "starting_github_authentication": "GitHub -Authentifizierung starten ...", "status_check_error": "Statusprüfungs<PERSON>hler: {<PERSON><PERSON>}", "account_is_still_valid": "Konto ist noch gültig (Nutzung: {Verwendung})", "authentication_timeout": "Authentifizierungszeitüberschreitung", "using_first_available_chrome_profile": "Verwenden Sie das erste verfügbare Chromprofil: {Profil}", "google_start": "Google Start", "usage_count": "Nutzungsanzahl: {<PERSON>erwen<PERSON>ng}", "no_compatible_browser_found": "<PERSON><PERSON> kompatibler Browser gefunden. Bitte installieren Sie Google Chrome oder Chrom.", "authentication_successful_getting_account_info": "Authentifizierung erfolgreich, Kontoinformationen erhalten ...", "found_chrome_at": "Chrome bei: {Path} gefunden", "error_getting_user_data_directory": "<PERSON><PERSON> beim Erhalten von Benutzerdatenverzeich<PERSON>sen: {<PERSON><PERSON>}", "error_finding_chrome_profile": "Fehlerfindungschromprofil unter Verwendung von Standard: {<PERSON><PERSON>}", "auth_update_success": "AUTH -UPDATE ERFORDERUNG", "authentication_successful": "Authentifizierung erfolgreich - E -Mail: {E -Mail}", "authentication_failed": "Authentifizierung fehlgeschlagen: {<PERSON><PERSON>}", "warning_browser_close": "Warnung: Die<PERSON> schließt alle laufenden {<PERSON><PERSON><PERSON>} -Prozesse", "supported_browsers": "Unterstützte Browser für {Plattform}", "authentication_button_not_found": "Authentifizierungstaste nicht gefunden", "starting_new_google_authentication": "Neue Google -Authentifizierung starten ...", "waiting_for_authentication": "Warten auf Authentifizierung ...", "found_default_chrome_profile": "Standardchromprofil gefunden", "starting_browser": "Starten Sie Browser bei: {Path}", "could_not_check_usage_count": "Die Nutzungsanzahl nicht überprüfen: {<PERSON><PERSON>}", "token_extraction_error": "Token -Extraktionsfehler: {<PERSON><PERSON>}", "profile_selection_error": "<PERSON><PERSON> während der Profilauswahl: {<PERSON><PERSON>}", "warning_could_not_kill_existing_browser_processes": "WARNUNG: Bestehende Browserprozesse nicht abtöten: {<PERSON><PERSON>}", "browser_failed_to_start": "Browser startete nicht: {<PERSON><PERSON>}", "redirecting_to_authenticator_cursor_sh": "Umleitung von Authenticator.cursor.sh ...", "starting_re_authentication_process": "Neuauthentifizierungsprozess beginnen ...", "found_browser_data_directory": "Fund Browser Data Directory: {Path}", "browser_not_found_trying_chrome": "Konnte {browser} nicht finden, das Chrome stattdessen ausprobiert", "found_cookies": "Gefunden {count} Cookies", "auth_update_failed": "Auth -Update fehlgeschlagen", "failed_to_delete_expired_account": "Nicht abgelaufenes Konto löschen", "browser_failed_to_start_fallback": "Browser startete nicht: {<PERSON><PERSON>}", "navigating_to_authentication_page": "Navigieren zur Authentifizierungsseite ...", "initializing_browser_setup": "<PERSON><PERSON><PERSON><PERSON> -Setup ...", "browser_closed": "Browser geschlossen", "failed_to_delete_account_or_re_authenticate": "Das Konto nicht löschen oder neu authentifiziert: {<PERSON><PERSON>}", "detected_platform": "Erkennete Plattform: {Plattform}", "failed_to_extract_auth_info": "Nicht extrahierte Auth -Info: {<PERSON><PERSON>}", "using_browser_profile": "<PERSON><PERSON><PERSON><PERSON> von Browserprofil: {Profil}", "starting_google_authentication": "Google Authentifizierung starten ...", "browser_failed": "Browser startete nicht: {<PERSON><PERSON>}", "consider_running_without_sudo": "Erwägen Sie das Skript ohne sudo auszuführen", "try_running_without_sudo_admin": "Versuchen Sie, ohne sudo/administratorische Privilegien auszuführen", "page_changed_checking_auth": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>th prüfen ...", "running_as_root_warning": "<PERSON>ür die Browserautomatisierung wird nicht empfohlen", "please_select_your_google_account_to_continue": "Bitte wählen Sie Ihr Google -Konto aus, um fortzufahren ...", "browser_setup_failed": "Browser -<PERSON><PERSON> fehlgeschlagen: {<PERSON><PERSON>}", "missing_authentication_data": "Fehlende Authentifizierungsdaten: {Daten}", "using_configured_browser_path": "Verwenden Sie konfiguriert {Browser} Pfad: {Path}", "killing_browser_processes": "<PERSON><PERSON><PERSON> {<PERSON><PERSON><PERSON>} Prozesse ...", "could_not_find_usage_count": "Nutzungsanzahl nicht finden: {<PERSON><PERSON>}", "browser_setup_completed": "Browser -<PERSON>up erfolgreich abgeschlossen", "account_has_reached_maximum_usage": "Das Konto hat die maximale Nutzung erreicht, {Löschen}", "could_not_find_email": "Konnte keine E -Mail finden: {<PERSON><PERSON>}", "user_data_dir_not_found": "{Browser} <PERSON><PERSON><PERSON><PERSON>nverzeichnis, das bei {Path} nicht gefunden wird, versucht stattdessen Chrome", "found_browser_user_data_dir": "Gefunden {<PERSON><PERSON><PERSON>} Benutzerdatenverzeichnis: {Path}", "invalid_authentication_type": "Ungültiger Authentifizierungstyp"}, "auth_check": {"token_length": "Tokenlänge: {<PERSON>änge} <PERSON><PERSON><PERSON>", "usage_response_status": "Verwendungsantwortstatus: {Antwort}", "operation_cancelled": "Operation durch den Benutzer abgebrochen", "checking_usage_information": "Nutzungsinformationen überprüfen ...", "error_getting_token_from_db": "<PERSON><PERSON>, das Token aus der Datenbank erhalten: {<PERSON><PERSON>}", "usage_response": "Verwendungsantwort: {Antwort}", "authorization_failed": "Autorisierung fehlgeschlagen!", "authorization_successful": "Autorisierung erfolgreich!", "check_error": "Fehlerprüfung Autorisierung: {<PERSON><PERSON>}", "request_timeout": "Zeitlich anfordern", "connection_error": "Verbindungsfehler", "invalid_token": "Ungültiges Token", "check_usage_response": "Überprüfen Sie die Nutzungsantwort: {Antwort}", "enter_token": "<PERSON><PERSON><PERSON> Sie Ihren Cursor -Token ein:", "user_unauthorized": "Der Benutzer ist nicht autorisiert", "token_found_in_db": "Token in der Datenbank gefunden", "checking_authorization": "Überprüfung der Autorisierung ...", "error_generating_checksum": "Fehlergenerierung Prüfsumme: {<PERSON><PERSON>}", "token_source": "Token aus der Datenbank bekommen oder manuell eingeben? (D/M, Standard: D)", "unexpected_error": "<PERSON><PERSON><PERSON><PERSON>: {<PERSON><PERSON>}", "user_authorized": "Der Benutzer ist autorisiert", "token_not_found_in_db": "Token nicht in der Datenbank gefunden", "jwt_token_warning": "Token scheint im JWT -Format zu sein, aber der API -Check hat einen unerwarteten Statuscode zurückgegeben. Das Token ist möglicherweise gültig, aber der API -Zugriff ist eingeschränkt.", "unexpected_status_code": "Unerwarteter Statuscode: {Code}", "getting_token_from_db": "Token aus der Datenbank zu bekommen ...", "cursor_acc_info_not_found": "Cursor_acc_info.py nicht gefunden"}, "account_delete": {"delete_input_not_found": "Bestätigungseingabe löschen, die nach mehreren Versuchen nicht gefunden wurden", "logging_in": "Anmelden bei Google ...", "confirm_button_not_found": "Bestätigen Sie die Taste, die nach mehreren Versuchen nicht gefunden wurde", "delete_button_clicked": "Klicken Sie auf die Schaltfläche Konto löschen", "confirm_button_error": "Fehlerfest -Bestätigungs -Taste: {<PERSON><PERSON>}", "confirm_prompt": "Sind <PERSON> sic<PERSON>, dass Sie fortfahren möchten? (y/n):", "delete_button_error": "Fehler finden Sie Schaltfläche Löschen: {<PERSON><PERSON>}", "cancelled": "Konto Löschung storniert.", "error": "<PERSON><PERSON> während der Kontoneletion: {<PERSON><PERSON>}", "interrupted": "Der vom Benutzer unterbrochene Konto -Löschungsprozess.", "delete_input_not_found_continuing": "Löschen Sie die Bestätigungseingabe, die nicht gefunden wurde, und versuche trotzdem weiterzumachen", "advanced_tab_retry": "Advanced Registerkarte Nicht gefunden, Versuch {Versuch}/{max_attempts}", "waiting_for_auth": "Warten auf die Google -Authentifizierung ...", "typed_delete": "In Bestätigungsbox \"Löschen\" eingeben", "trying_settings": "<PERSON><PERSON><PERSON>, zur Seite \"Einstellungen\" zu navigieren ...", "email_not_found": "E -Mail nicht gefunden: {<PERSON><PERSON>}", "delete_input_retry": "Eingabe löschen nicht gefunden, versuchen {Versuch}/{max_attempts}", "delete_button_not_found": "Die Kontotaste löschen, die nach mehreren Versuchen nicht gefunden wurden", "already_on_settings": "Bereits auf Einstellungsseite", "failed": "Das Konto -Löschungsprozess ist fehlgeschlagen oder wurde storniert.", "warning": "Warnung: Dies löscht Ihr Cursorkonto dauerhaft. Diese Aktion kann nicht rückgängig gemacht werden.", "direct_advanced_navigation": "Versuchen Sie die direkte Navigation auf Advanced Registerkarte", "advanced_tab_not_found": "<PERSON><PERSON><PERSON><PERSON><PERSON> Register<PERSON> nicht nach mehreren Versuchen gefunden", "auth_timeout": "Authentifizierungszeitüberschreitung, trotzdem fortgesetzt ...", "select_google_account": "Bitte wählen Sie Ihr Google -Konto aus ...", "google_button_not_found": "Google Login -Schaltfläche nicht gefunden", "found_danger_zone": "Gefahrenzonenabschnitt gefunden", "account_deleted": "Konto erfolgreich gelöscht!", "advanced_tab_error": "FEHLER FORDERUNG ERWEITERTE TAB: {ERROR}", "starting_process": "Löschungsprozess des Kontos starten ...", "delete_button_retry": "Schaltfl<PERSON>che nicht gefunden, versuchen Sie {Versuch}/{max_attempts}", "login_redirect_failed": "Die Anmeldung umgeleitet und versuchte direkte Navigation ...", "unexpected_error": "<PERSON><PERSON><PERSON><PERSON>: {<PERSON><PERSON>}", "delete_input_error": "<PERSON><PERSON> finden", "login_successful": "Erfolgreich anmelden", "advanced_tab_clicked": "Klickte auf die Registerkarte Erweitert", "unexpected_page": "Unerwartete Seite nach Anmeldung: {url}", "found_email": "E -Mail gefunden: {E -Mail}", "title": "Cursor Google -Konto <PERSON>ungstool", "navigating_to_settings": "Navigieren zur Seite \"Einstellungen\" ...", "success": "<PERSON>hr Cursorkonto wurde erfolgreich gelöscht!", "confirm_button_retry": "Nicht gefundene Schaltfläche bestätigen, Versuch {Versuch}/{max_attempts}"}, "manual_auth": {"auth_type_selected": "Ausgewählter Authentifizierungstyp: {Typ}", "proceed_prompt": "Fortfahren? (y/n):", "auth_type_github": "<PERSON><PERSON><PERSON>", "confirm_prompt": "Bitte bestätigen Sie die folgenden Informationen:", "invalid_token": "Ungültiges Token. Authentifizierung abgebrochen.", "continue_anyway": "Trotzdem fortfahren? (y/n):", "token_verified": "Token verifiziert erfolgreich!", "error": "<PERSON><PERSON>: {<PERSON><PERSON>}", "auth_update_failed": "Die Authentifizierungsinformationen nicht aktualisieren", "auth_type_auth0": "Auth_0 (Standard)", "auth_type_prompt": "Wählen Sie Authentifizierungstyp:", "verifying_token": "Überprüfung der Gültigkeit der Token ...", "auth_updated_successfully": "Authentifizierungsinformationen erfolgreich aktualisiert!", "email_prompt": "E -Mail eingeben (für zufällige E -Mails leer lassen):", "token_prompt": "Geben Sie Ihr Cursor -Token (Access_Token/refresh_token) ein:", "title": "Manuelle Cursorauthentifizierung", "token_verification_skipped": "Token -Überprüfung übersprungen (check_user_authorized.py nicht gefunden)", "random_email_generated": "Zufällige E -Mail generiert: {E -Mail}", "token_required": "Token ist erforderlich", "auth_type_google": "Google", "operation_cancelled": "Operation storniert", "token_verification_error": "Fehlerüberprüfung der Token: {<PERSON><PERSON>}", "updating_database": "Aktualisieren von Cursorauthentifizierungsdatenbank ..."}, "token": {"refreshing": "Erfrischendes Token ...", "extraction_error": "<PERSON><PERSON> beim <PERSON> von <PERSON>: {<PERSON><PERSON>}", "invalid_response": "Ungültige JSON -Antwort vom Refresh -Server", "no_access_token": "<PERSON>in <PERSON> als Antwort", "connection_error": "Verbindungsfehler zum aktuellen Server", "unexpected_error": "Unerwarteter Fehler während der Token -Aktualisierung: {<PERSON><PERSON>}", "server_error": "Serverfehler aktualisieren: http {Status}", "refresh_success": "Token wurde erfolgreich erfrischt! Gültig für {Tage} Tage (abläuft: {Ablauf})", "request_timeout": "Anfrage zum Aktualisieren des Servers zeitlich festgelegt", "refresh_failed": "Token -Aktualisierung fehlgeschlagen: {<PERSON><PERSON>}"}, "browser_profile": {"profile_selected": "Ausgewähltes Profil: {Profil}", "default_profile": "Standardprofil", "no_profiles": "<PERSON><PERSON> {<PERSON><PERSON><PERSON>} -Profile gefunden", "select_profile": "<PERSON><PERSON><PERSON><PERSON> {<PERSON><PERSON><PERSON>} -Profile aus, um zu verwenden:", "error_loading": "<PERSON><PERSON> laden {<PERSON><PERSON><PERSON>} Profile: {<PERSON><PERSON>}", "invalid_selection": "Ungültige Auswahl. Bitte versuchen Sie es erneut.", "title": "Browser -<PERSON><PERSON><PERSON><PERSON><PERSON>", "profile": "Profil {<PERSON><PERSON><PERSON>}", "profile_list": "Verfügbar {Browser} Profile:"}, "github_register": {"feature2": "Registriert ein neues Github -Konto mit zufälligen Anmeldeinformationen.", "feature6": "Speichert alle Anmeldeinformationen in einer Datei.", "starting_automation": "Automatisierung beginnen ...", "feature1": "Generiert eine temporäre E -Mail mit 1secmail.", "title": "GitHub + Cursor AI Registrierung Automatisierung", "github_username": "<PERSON><PERSON><PERSON>", "check_browser_windows_for_manual_intervention_or_try_again_later": "Überprüfen Sie die Browserfenster auf manuelle Eingriffe oder versuchen Si<PERSON> es später erneut.", "warning1": "Dieses Skript automatisiert die Kontoerstellung, die möglicherweise gegen GitHub/Cursor -Nutzungsbedingungen verstoßen.", "feature4": "Logg<PERSON> sich in Cursor AI unter Verwendung der Github -Authentifizierung an.", "invalid_choice": "Ungültige Auswahl. Bitte geben Si<PERSON> '<PERSON>' <PERSON><PERSON> '<PERSON>' ein", "completed_successfully": "GitHub + Cursor -Registrierung erfolgreich abgeschlossen!", "warning2": "Benötigt Internetzugang und administrative Berechtigungen.", "registration_encountered_issues": "GitHub + Cursorregistrierung trafen auf Probleme.", "credentials_saved": "Diese Anmeldeinformationen wurden auf github_cursor_accounts.txt gespeichert", "feature3": "Überprüft die GitHub -E -Mail automatisch.", "github_password": "GitHub Passwort", "features_header": "Merkmale", "feature5": "Setzt die Maschinen -ID zurück, um die Versuchserkennung zu umgehen.", "warning4": "Verwenden Sie verantwortungsbewusst und auf eigenes Risiko.", "warning3": "Captcha oder zusätzliche Überprüfung kann die Automatisierung unterbrechen.", "cancelled": "Operation storniert", "warnings_header": "Warnungen", "program_terminated": "Programm vom Benutzer beendet", "confirm": "Sind Si<PERSON> sicher, dass Sie fortfahren möchten?", "email_address": "E-Mail-Adresse"}, "account_info": {"subscription": "Abonnement", "failed_to_get_account_info": "<PERSON>s wurden keine Kontoinformationen erhalten", "subscription_type": "Abonnementtyp", "pro": "Pro", "failed_to_get_account": "<PERSON>s wurden keine Kontoinformationen erhalten", "config_not_found": "Konfiguration nicht gefunden.", "premium_usage": "Premium -Nutzung", "failed_to_get_subscription": "Abonnementinformationen nicht erhalten", "basic_usage": "Grundnutzung", "premium": "Prämie", "free": "<PERSON><PERSON>", "email_not_found": "E -Mail nicht gefunden", "title": "Kontoinformationen", "inactive": "Inaktiv", "remaining_trial": "Verbleibender Versuch", "enterprise": "Unternehmen", "lifetime_access_enabled": "Lebensdauer Zugang aktiviert", "failed_to_get_usage": "Nutzungsinformationen nicht erhalten", "usage_not_found": "Nutzung nicht gefunden", "days_remaining": "Verbleibende Tage", "failed_to_get_token": "Versäumte es, Token zu bekommen", "token": "Token", "subscription_not_found": "Abonnementinformationen nicht gefunden", "days": "Tage", "team": "Team", "token_not_found": "Token nicht gefunden", "pro_trial": "Pro Test", "active": "Aktiv", "email": "E-Mail", "failed_to_get_email": "Die E -Mail -Adresse nicht erhalten", "trial_remaining": "Verbleibender Pro -Versuch", "usage": "Verwendung"}, "config": {"config_updated": "Konfiguration aktualisiert", "configuration": "Konfiguration", "file_owner": "Dateibesitzer: {<PERSON><PERSON><PERSON><PERSON><PERSON>}", "error_checking_linux_paths": "Fehlerprüfung Linux -Pfade: {<PERSON><PERSON>}", "storage_file_is_empty": "Speicherdatei ist leer: {Storage_path}", "config_directory": "Konfigurationsverzeichnis", "documents_path_not_found": "Dokumente Pfad nicht gefunden, unter Verwendung des aktuellen Verzeichnisses", "config_not_available": "Konfiguration nicht verfügbar", "neither_cursor_nor_cursor_directory_found": "Weder Cursor noch Cursorverzeichnis in {config_base} gefunden", "please_make_sure_cursor_is_installed_and_has_been_run_at_least_once": "<PERSON>te stellen Si<PERSON> sicher, dass der Cursor installiert ist und mindestens einmal ausgeführt wurde", "using_temp_dir": "<PERSON>erwen<PERSON> von temporärem Verzeichnis aufgrund von <PERSON>: {Path} (<PERSON><PERSON>: {<PERSON><PERSON>})", "config_created": "Konfiguration erstellt: {config_file}", "storage_file_not_found": "Speicherdatei nicht gefunden: {Storage_path}", "the_file_might_be_corrupted_please_reinstall_cursor": "Die Datei kann beschädigt sein. Bitte installieren Sie den Cursor neu.", "error_getting_file_stats": "<PERSON><PERSON> Er<PERSON><PERSON> von <PERSON>: {<PERSON><PERSON>}", "enabled": "Ermöglicht", "backup_created": "Sicherung erstellt: {Path}", "file_permissions": "Dateiberechtigungen: {Berechtigungen}", "config_setup_error": "<PERSON><PERSON> einrichten Konfiguration: {<PERSON><PERSON>}", "config_removed": "Konfigurationsdatei für erzwungene Aktualisierung entfernt", "config_force_update_enabled": "Konfigurationsdatei -Kraft -Update aktiviert und erzwungenes Update durchführen", "error_reading_storage_file": "<PERSON><PERSON> beim Lesen der Speicherdatei: {<PERSON><PERSON>}", "file_size": "Dateigröße: {Größe} Bytes", "config_force_update_disabled": "Konfigurationsdatei -Kraft -Aktualisierung deaktiviert, überspringen erzwungenes Update", "config_dir_created": "Konfigurationsverzeichnis erstellt: {Path}", "config_option_added": "Konfigurationsoption hinzugefügt: {Option}", "file_group": "Dateigruppe: {Gruppe}", "and": "Und", "force_update_failed": "Force -Update -Konfiguration fehlgeschlagen: {<PERSON><PERSON>}", "storage_directory_not_found": "Speicherverzeichnis nicht gefunden: {Storage_dir}", "also_checked": "Auch überprüft {Path}", "storage_file_found": "Speicherdatei gefunden: {Storage_path}", "disabled": "Deaktiviert", "try_running": "Versuchen Sie das Ausführen: {<PERSON><PERSON><PERSON>}", "backup_failed": "Failed to backup config: {error}", "storage_file_is_valid_and_contains_data": "Die Speicherdatei ist gültig und enthält Daten", "permission_denied": "Erlaubnis abgelehnt: {Storage_path}"}, "bypass": {"found_product_json": "Found Product.json: {Path}", "starting": "Cursorversion Bypass starten ...", "version_updated": "Version von {Old} zu {new} aktualisiert", "menu_option": "Bypass Cursor Versionsprüfung", "unsupported_os": "Nicht unterstütztes Betriebssystem: {System}", "backup_created": "Sicherung erstellt: {Path}", "current_version": "Aktuelle Version: {Version}", "localappdata_not_found": "LocalAppdata -Umgebungsvariable nicht gefunden", "no_write_permission": "<PERSON><PERSON> Schreibberechtigung für Datei: {Path}", "write_failed": "Product nicht geschrieben.json: {error}", "description": "Dieses Tool modifiziert Cursors product.json, um die Versionsbeschränkungen zu umgehen", "bypass_failed": "Versionsbypass fehlgeschlagen: {<PERSON><PERSON>}", "title": "Cursorversion Bypass -Tool", "no_update_needed": "Kein Update erforderlich. Die aktuelle Version {Version} ist bereits> = 0,46.0", "read_failed": "Product nicht gelesen.json: {error}", "stack_trace": "Stapelspur", "product_json_not_found": "product.json nicht in gemeinsamen Linux -Pfaden gefunden", "file_not_found": "Datei nicht gefunden: {Path}"}, "bypass_token_limit": {"description": "Dieses Tool modifiziert die Datei workbench.desktop.main.js, um die Token -Grenze zu umgehen", "press_enter": "Drücken Sie die Eingabetaste, um fortzufahren ...", "title": "Bypass Token Limit Tool"}, "tempmail": {"no_email": "<PERSON>ine Cursorüberprüfungs -E -Mail gefunden", "general_error": "Es ist ein Fehler aufgetreten: {<PERSON><PERSON>}", "config_error": "Konfigurations<PERSON><PERSON><PERSON>: {<PERSON><PERSON>}", "checking_email": "Überprüfung nach Cursor -Überprüfungs -E -Mail ...", "extract_code_failed": "Verifizierungscode extrahieren fehlgeschlagen: {<PERSON><PERSON>}", "configured_email": "Konfigurierte E -Mail: {E -Mail}", "no_code": "Konnte keinen Bestätigungscode erhalten", "check_email_failed": "Überprüfen Sie die E -Mail fehlgeschlagen: {<PERSON><PERSON>}", "email_found": "Gefundene Cursor -Überprüfungs -E -Mail gefunden", "verification_code": "Überprüfungscode: {Code}"}}