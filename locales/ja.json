{"menu": {"title": "利用可能なオプション", "exit": "プログラムを終了", "reset": "マシンIDをリセット", "register": "新しいCursorアカウントを登録", "register_google": "Googleアカウントで登録", "register_github": "GitHubアカウントで登録", "register_manual": "カスタムメールでCursorを登録", "quit": "Cursorアプリケーションを閉じる", "select_language": "言語を変更", "select_chrome_profile": "Chromeプロファイルを選択", "input_choice": "選択肢を入力してください ({choices})", "invalid_choice": "無効な選択です。{choices}から数字を入力してください", "program_terminated": "プログラムはユーザーによって終了されました", "error_occurred": "エラーが発生しました: {error}。もう一度お試しください", "press_enter": "終了するにはEnterキーを押してください", "disable_auto_update": "Cursorの自動更新を無効化", "lifetime_access_enabled": "ライフタイムアクセスが有効化されました", "totally_reset": "Cursorを完全にリセット", "outdate": "期限切れ", "temp_github_register": "一時的なGitHub登録", "admin_required": "実行ファイルとして実行中、管理者権限が必要です。", "admin_required_continue": "管理者権限なしで続行します。", "coming_soon": "近日公開", "fixed_soon": "近日修正予定", "contribute": "プロジェクトに貢献", "config": "設定を表示", "delete_google_account": "CursorのGoogleアカウントを削除", "continue_prompt": "続行しますか？(y/N): ", "operation_cancelled_by_user": "操作はユーザーによってキャンセルされました", "exiting": "終了中……", "bypass_version_check": "Cursorのバージョンチェックをバイパス", "check_user_authorized": "ユーザーの認証を確認", "bypass_token_limit": "トークン制限をバイパス", "language_config_saved": "言語設定が正常に保存されました", "lang_invalid_choice": "無効な選択です。以下のオプションから選択してください: ({lang_choices})", "restore_machine_id": "バックアップからマシンIDを復元", "manual_custom_auth": "手動カスタム認証"}, "languages": {"ar": "アラビア語", "en": "英語", "zh_cn": "簡体字中国語", "zh_tw": "繁体字中国語", "vi": "ベトナム語", "nl": "オランダ語", "de": "ドイツ語", "fr": "フランス語", "pt": "ポルトガル語", "ru": "ロシア語", "tr": "トルコ語", "bg": "ブルガリア語", "es": "スペイン語", "ja": "日本語", "it": "イタリア語"}, "register": {"waiting_for_second_verification": "電子メールの確認を待っています...", "browser_started": "ブラウザは正常にオープンしました", "config_updated": "config updated", "manual_email_input": "手動の電子メール入力", "verification_start": "確認コードの取得を開始します", "last_name": "苗字", "press_enter": "Enterを押して終了します", "get_verification_code_timeout": "検証コードタイムアウトを取得します", "detect_turnstile": "セキュリティ検証の確認...", "start_getting_verification_code": "確認コードの取得を開始し、60年代に試してみます", "human_verify_error": "ユーザーが人間であることを確認できません。再試行...", "token_failed": "トークンに失敗する：{エラー}", "verification_not_found": "検証コードは見つかりません", "getting_code": "検証コードを取得すると、60年代に試してみます", "turnstile_passed": "ターンスタイルが通過しました", "email_address": "電子メールアドレス", "setting_on_password": "パスワードの設定", "login_success_and_jump_to_settings_page": "成功をログインし、[設定]ページにジャンプします", "register_start": "登録を開始します", "verification_failed": "検証に失敗しました", "title": "カーソル登録ツール", "visiting_url": "訪問URL", "try_get_code": "試してみてください| {pirch}検証コードを取得|残りの時間：{時間} s", "try_get_verification_code": "試してみてください| {pirch}検証コードを取得|残りの時間：{resight_time} s", "open_mailbox": "メールボックスページを開く", "filling_form": "フォームに記入します", "no_turnstile": "ターンスタイルを検出しないでください", "set_password": "パスワードを設定します", "waiting_for_page_load": "ページの読み込み...", "browser_path_invalid": "{ブラウザー}パスは、デフォルトパスを使用して無効です", "account_error": "アカウント情報が失敗します：{エラー}", "detect_login_page": "ログインページを検出し、ログインを開始します...", "manual_code_input": "手動コード入力", "using_browser_profile": "{browser}プロファイルを使用：{user_data_dir}", "verification_code_filled": "検証コードが記入されています", "password_error": "パスワードを設定できませんでした：{エラー}。もう一度やり直してください", "reset_machine_id": "マシンIDをリセットします", "cursor_auth_info_updated": "カーソル認証情報が更新されました", "form_submitted": "フォーム送信、検証を開始...", "handling_turnstile": "セキュリティ検証の処理...", "handle_turnstile": "ターンスタイルを処理します", "password_submitted": "提出されたパスワード", "email_error": "メールアドレスを取得できませんでした", "cursor_auth_info_update_failed": "カーソル認証情報の更新は失敗しました", "total_usage": "合計使用量：{使用}", "exit_signal": "終了信号", "make_sure_browser_is_properly_installed": "{ブラウザー}が適切にインストールされていることを確認してください", "verification_error": "検証エラー：{エラー}", "get_verification_code_success": "検証コードの成功を取得します", "starting_browser": "オープニングブラウザ...", "get_token": "カーソルセッショントークンを取得します", "config_created": "作成された構成", "register_process_error": "登録プロセスエラー：{エラー}", "using_browser": "{browser}ブラウザの使用：{path}", "tracking_processes": "追跡{count} {browser}プロセス", "max_retries_reached": "最大再試行に達しました。登録に失敗しました。", "browser_start": "起動ブラウザ", "first_verification_passed": "最初の検証が成功しました", "setting_password": "パスワードの設定", "account_info_saved": "アカウント情報が保存されています", "form_success": "フォームが正常に送信されました", "get_account": "アカウント情報を取得します", "get_email_address": "メールアドレスを取得します", "verification_timeout": "検証コードタイムアウトを取得します", "basic_info_submitted": "提出された基本情報", "first_name": "ファーストネーム", "setup_error": "電子メールのセットアップエラー：{エラー}", "waiting_for_verification_code": "確認コードを待っています...", "cursor_registration_completed": "カーソル登録が完了しました！", "token_max_attempts": "到達最大試み（{max}）|トークンを取得できませんでした", "save_account_info_failed": "アカウント情報の保存失敗", "could_not_track_processes": "{browser}プロセスを追跡できませんでした：{エラー}", "start": "登録プロセスの開始...", "password_success": "パスワードを正常に設定します", "password": "パスワード", "no_new_processes_detected": "追跡するために検出された新しい{ブラウザー}プロセスはありません", "basic_info": "提出された基本情報", "verification_success": "セキュリティ検証は成功しました", "config_option_added": "configオプション追加：{オプション}", "mailbox": "電子メールの受信トレイに正常にアクセスしました", "token_attempt": "試してみてください| {Tirem} Times Token | {time}で再試行します", "try_install_browser": "パッケージマネージャーと一緒にブラウザをインストールしてみてください", "retry_verification": "検証を再試行...", "update_cursor_auth_info": "カーソル認証情報を更新します", "token_success": "トークンの成功を取得します", "tempmail_plus_verification_completed": "tempmailplus検証は正常に完了しました", "tempmail_plus_initialized": "TempMailplusは正常に初期化されました", "tempmail_plus_epin_missing": "tempmailplus epinは構成されていません", "using_tempmail_plus": "電子メール検証にtempmailplusを使用します", "tempmail_plus_verification_failed": "tempmailplus検証が失敗しました：{エラー}", "tempmail_plus_email_missing": "tempmailplus電子メールは構成されていません", "tempmail_plus_disabled": "tempmailplusは無効です", "tempmail_plus_verification_started": "開始tempmailplus検証プロセス", "tempmail_plus_enabled": "tempmailplusが有効になっています", "tempmail_plus_config_missing": "tempmailplus構成がありません", "tempmail_plus_init_failed": "tempmailplusの初期化に失敗しました：{エラー}"}, "config": {"backup_failed": "configにバックアップに失敗しました：{error}", "enabled": "有効になっています", "try_running": "実行してみてください：{コマンド}", "permission_denied": "許可拒否：{Storage_Path}", "neither_cursor_nor_cursor_directory_found": "{config_base}で見つかったカーソルもカーソルディレクトリもありません", "storage_file_found": "見つかったストレージファイル：{Storage_Path}", "please_make_sure_cursor_is_installed_and_has_been_run_at_least_once": "カーソルがインストールされており、少なくとも1回は実行されていることを確認してください", "file_group": "ファイルグループ：{グループ}", "the_file_might_be_corrupted_please_reinstall_cursor": "ファイルが破損している可能性があります。カーソルを再インストールしてください", "using_temp_dir": "エラーによる一時的なディレクトリの使用：{path}（エラー：{エラー}）", "documents_path_not_found": "現在のディレクトリを使用して、パスが見つかりません", "and": "そして", "storage_file_is_empty": "ストレージファイルは空です：{Storage_Path}", "storage_directory_not_found": "ストレージディレクトリが見つかりません：{Storage_dir}", "config_created": "構成作成：{config_file}", "config_not_available": "構成は使用できません", "file_size": "ファイルサイズ：{サイズ}バイト", "also_checked": "{パス}もチェックしました", "config_removed": "強制更新のために削除された構成ファイル", "error_getting_file_stats": "エラーファイル統計の取得：{エラー}", "force_update_failed": "フォースアップデート構成が失敗しました：{エラー}", "disabled": "無効", "config_force_update_enabled": "Config File Force Updateが有効になり、強制更新が実行されます", "error_checking_linux_paths": "Linuxパスのエラーチェック：{エラー}", "backup_created": "作成されたバックアップ：{パス}", "config_setup_error": "設定のエラー：{エラー}", "config_directory": "構成ディレクトリ", "file_owner": "ファイル所有者：{所有者}", "config_force_update_disabled": "Config File Force Updateは無効になり、強制更新をスキップします", "config_option_added": "configオプション追加：{オプション}", "config_updated": "config updated", "config_dir_created": "作成された構成ディレクトリ：{path}", "storage_file_is_valid_and_contains_data": "ストレージファイルは有効で、データが含まれています", "error_reading_storage_file": "エラーストレージファイルの読み取り：{エラー}", "storage_file_not_found": "Storageファイルが見つかりません：{Storage_Path}", "file_permissions": "ファイル許可：{permissions}", "configuration": "構成"}, "restore": {"update_macos_system_ids_failed": "MacOSシステムIDの更新に失敗しました：{エラー}", "current_backup_created": "現在のストレージファイルのバックアップを作成しました", "current_file_not_found": "現在のストレージファイルが見つかりません", "windows_machine_guid_updated": "Windows Machine GUIDは正常に更新されました", "please_enter_number": "有効な番号を入力してください", "title": "バックアップからマシンIDを復元します", "machine_id_updated": "MachineIDファイルは正常に更新されました", "update_failed": "ストレージファイルの更新に失敗しました：{エラー}", "sqlite_updated": "SQLiteデータベースは正常に更新されました", "starting": "開始マシンID復元プロセス", "select_backup": "バックアップを選択して復元します", "sqm_client_key_not_found": "sqmclientレジストリキーが見つかりません", "update_windows_system_ids_failed": "WindowsシステムIDの更新に失敗しました：{エラー}", "missing_id": "IDがありません：{id}", "sqlite_not_found": "SQLiteデータベースが見つかりません", "updating_pair": "キー価値ペアの更新", "to_cancel": "キャンセルします", "storage_updated": "ストレージファイルが正常に更新されました", "read_backup_failed": "バックアップファイルの読み取りに失敗しました：{エラー}", "success": "マシンIDは正常に復元されました", "permission_denied": "許可が拒否されました。管理者として実行してみてください", "update_windows_machine_guid_failed": "WindowsマシンGUIDの更新に失敗しました：{エラー}", "operation_cancelled": "操作はキャンセルされました", "updating_sqlite": "SQLiteデータベースの更新", "sqlite_update_failed": "sqliteデータベースの更新に失敗しました：{エラー}", "failed_to_execute_plutil_command": "Plutilコマンドの実行に失敗しました", "update_windows_machine_id_failed": "WindowsマシンIDの更新に失敗しました：{エラー}", "confirm": "これらのIDを復元したいですか？", "updating_system_ids": "システムIDの更新", "system_ids_update_failed": "システムIDの更新に失敗しました：{エラー}", "machine_id_backup_created": "MachineIDファイルのバックアップを作成しました", "machine_id_update_failed": "MachineIDファイルの更新に失敗しました：{エラー}", "invalid_selection": "無効な選択", "ids_to_restore": "復元するマシンID", "backup_creation_failed": "バックアップの作成に失敗しました：{エラー}", "no_backups_found": "バックアップファイルは見つかりません", "available_backups": "利用可能なバックアップファイル", "macos_platform_uuid_updated": "MacOSプラットフォームUUIDは正常に更新されました", "press_enter": "Enterを押して続行します", "process_error": "プロセスエラーの復元：{エラー}", "windows_machine_id_updated": "Windows Machine IDは正常に更新されました"}, "totally_reset": {"delete_button_error": "エラー検索削除ボタン：{エラー}", "login_redirect_failed": "ログインリダイレクトが失敗し、直接ナビゲーションを試みます...", "reset_log_3": "カーソルAIを再インストールする必要があり、これで新鮮な試用期間が必要です。", "note_complete_machine_id_reset_may_require_running_as_administrator": "注：完全なマシンIDリセットは、管理者として実行する必要がある場合があります", "feature_2": "AIの履歴やプロンプトを含むすべてのキャッシュデータをクリアします", "reset_log_7": "カーソルAIのウェブサイトにアクセスする前に、ブラウザのクッキーとキャッシュをクリアします", "reset_log_9": "問題が発生した場合は、GitHub Issue Trackerにアクセスして、https：//github.com/yeongpin/cursor-free-vip/issuesで問題を作成します", "warning_4": "ターゲットカーソルAIエディターファイルと試行検出メカニズムのみをターゲットにします。", "windows_registry_instructions_2": "「regedit」を実行し、hkey_current_user \\ software \\の下で「カーソル」または「cursorai」を含むキーを検索して削除します。", "error": "カーソルリセットに失敗しました：{エラー}", "navigating_to_settings": "[設定]ページへのナビゲート...", "success": "カーソルリセットに正常にリセットされます", "feature_9": "Windows、MacOS、Linuxと互換性があります", "press_enter": "Enterを押して終了します", "advanced_tab_not_found": "複数の試行後には高度なタブが見つかりません", "deep_scanning": "追加のトライアル/ライセンスファイルのディープスキャンを実行します", "warning_5": "システム上の他のアプリケーションは影響を受けません。", "failed_to_delete_file_or_directory": "ファイルまたはディレクトリの削除に失敗しました：{path}", "cursor_reset_failed": "カーソルAIエディターリセットに失敗しました：{エラー}", "feature_title": "特徴", "reset_log_6": "利用可能な場合は、VPNを使用してIPアドレスを変更します", "delete_input_error": "ERRORの検索入力の削除：{エラー}", "feature_3": "マシンIDをリセットして試験検出をバイパスします", "db_connection_error": "データベースへの接続に失敗しました：{エラー}", "error_creating_machine_id": "マシンIDファイルの作成エラー{PATH}：{エラー}", "process_interrupted": "中断されたプロセス。終了...", "reset_log_5": "新しいトライアルに登録するときに別のメールアドレスを使用する", "resetting_cursor": "カーソルAIエディターのリセット...待ってください。", "reset_log_2": "変更を有効にするためにシステムを再起動してください。", "title": "完全にカーソルをリセットします", "cursor_reset_cancelled": "カーソルAIエディターリセットキャンセル。変更せずに終了します。", "advanced_tab_clicked": "[詳細]タブをクリックしました", "completed_in": "{時間}秒で完了します", "confirm_6": "このツールを実行した後、再びカーソルAIをセットアップする必要があります。", "connected_to_database": "データベースに接続されています", "resetting_machine_id": "試行検出をバイパスするためにマシン識別子をリセット...", "checking_for_electron_localstorage_files": "電子ローカルストレージファイルの確認", "already_on_settings": "すでに設定ページにあります", "delete_button_not_found": "複数の試行後にアカウントボタンを削除しません", "disclaimer_title": "免責事項", "linux_machine_id_modification_skipped": "Linux Machine-IDの変更スキップ：{エラー}", "keyboard_interrupt": "ユーザーが中断したプロセス。終了...", "failed_to_reset_machine_guid": "マシンGUIDのリセットに失敗しました", "confirm_4": "ターゲットカーソルAIエディターファイルと試行検出メカニズムのみをターゲットにします。", "confirm_title": "先に進みたいですか？", "warning_2": "構成、およびキャッシュデータ。このアクションを元に戻すことはできません。", "reading_config": "現在の構成を読み取ります", "updating_pair": "キー価値ペアの更新", "found_additional_potential_license_trial_files": "{count}追加の潜在的なライセンス/トライアルファイルが見つかりました", "failed_to_delete_file": "ファイルの削除に失敗しました：{path}", "disclaimer_7": "あなた自身の責任で使用してください", "performing_deep_scan": "追加のトライアル/ライセンスファイルのディープスキャンを実行します", "database_connection_closed": "データベース接続が閉じられました", "deleted": "削除：{パス}", "feature_8": "非装飾用ファイルとアプリケーションを安全に保存します", "unexpected_error": "予期しないエラーが発生しました：{エラー}", "warning_7": "あなた自身の責任で使用してください", "warning_3": "コードファイルは影響を受けず、ツールは設計されています", "confirm_7": "あなた自身の責任で使用してください", "delete_button_retry": "削除ボタンが見つかりません、{pirte}/{max_attempts}を削除します}", "feature_4": "新しいランダム化されたマシン識別子を作成します", "delete_input_not_found_continuing": "とにかく続行しようとしている確認の入力を削除します", "not_found": "ファイルが見つかりません：{path}", "advanced_tab_error": "ERRORINSING ADVANCED TAB：{エラー}", "reset_log_8": "問題が発生した場合は、別の場所にカーソルAIをインストールしてみてください", "windows_machine_id_modification_skipped": "Windows Machine IDの変更スキップ：{エラー}", "press_enter_to_return_to_main_menu": "Enterを押してメインメニューに戻ります...", "db_permission_error": "データベースファイルにアクセスできません。許可を確認してください", "removing_electron_localstorage_files_completed": "Electron LocalStorageファイルの削除が完了しました", "delete_input_retry": "入力が見つかっていない削除、{pirte}/{max_attempts}", "confirm_2": "構成、およびキャッシュデータ。このアクションを元に戻すことはできません。", "reset_log_4": "最良の結果については、考えてみてください。", "saving_new_config": "JSONに新しい構成を保存します", "invalid_choice": "「Y」または「n」を入力してください", "note_complete_system_machine_id_reset_may_require_sudo_privileges": "注：System Machine-IDリセットが必要になる場合は、SUDO特権が必要になる場合があります", "electron_localstorage_files_removed": "Electron LocalStorageファイルが削除されました", "config_not_found": "構成ファイルが見つかりません", "no_permission": "構成ファイルの読み取りまたは書き込みはできません。ファイル許可を確認してください", "confirm_1": "このアクションは、すべてのカーソルAI設定を削除します。", "no_additional_license_trial_files_found_in_deep_scan": "ディープスキャンに追加のライセンス/試用ファイルは見つかりません", "disclaimer_2": "構成、およびキャッシュデータ。このアクションを元に戻すことはできません。", "checking_config": "構成ファイルの確認", "disclaimer_4": "ターゲットカーソルAIエディターファイルと試行検出メカニズムのみをターゲットにします。", "feature_7": "非表示ライセンスおよび試用関連ファイルのディープスキャン", "warning_title": "警告", "skipped_for_safety": "安全のためにスキップ（カーソル関連ではない）：{パス}", "disclaimer_3": "コードファイルは影響を受けず、ツールは設計されています", "disclaimer_1": "このツールは、すべてのカーソルAI設定を永続的に削除します。", "removed": "削除：{パス}", "report_issue": "この問題をhttps://github.com/yeongpin/cursor-free-vip/issues（", "found_danger_zone": "Danger Zoneセクションが見つかりました", "creating_backup": "構成バックアップの作成", "cursor_reset_completed": "カーソルAIエディターは完全にリセットされ、試行検出がバイパスされました！", "operation_cancelled": "操作はキャンセルされました。変更せずに終了します。", "direct_advanced_navigation": "[Advanced Tab]に直接ナビゲーションを試します", "removing_electron_localstorage_files": "電子ローカルストレージファイルの削除", "disclaimer_5": "システム上の他のアプリケーションは影響を受けません。", "feature_6": "試行情報とアクティベーションデータをリセットします", "created_extended_trial_info": "新しい拡張トライアル情報を作成した：{Path}", "failed_to_remove": "削除に失敗しました：{path}", "db_not_found": "データベースファイルが見つかりません：{path}", "backup_exists": "バックアップファイルはすでに存在し、バックアップステップをスキップします", "error_searching": "{path}でファイルを検索するエラー：{エラー}", "feature_1": "カーソルAIの設定と構成の完全な削除", "error_deleting": "エラー削除{path}：{エラー}", "error_creating_trial_info": "トライアル情報ファイルの作成エラー{path}：{error}", "delete_button_clicked": "[アカウントの削除]ボタンをクリックしました", "reset_machine_id": "マシンIDをリセットします", "generating_new_machine_id": "新しいマシンIDの生成", "warning_1": "このアクションは、すべてのカーソルAI設定を削除します。", "failed_to_delete_directory": "ディレクトリの削除に失敗しました：{path}", "resetting_cursor_ai_editor": "カーソルAIエディターのリセット...待ってください。", "windows_registry_instructions": "📝注：Windowsで完全にリセットするには、レジストリエントリをクリーンする必要がある場合もあります。", "feature_5": "カスタム拡張機能と設定を削除します", "removing_known": "既知のトライアル/ライセンスファイルの削除", "created_machine_id": "新しいマシンIDを作成しました：{パス}", "disclaimer_6": "このツールを実行した後、再びカーソルAIをセットアップする必要があります。", "database_updated_successfully": "データベースは正常に更新されました", "advanced_tab_retry": "高度なタブが見つかりません、試み{pirter}/{max_attempts}", "return_to_main_menu": "メインメニューに戻る...", "electron_localstorage_files_removal_error": "エラー電子localStorageファイルの削除エラー：{エラー}", "reset_log_1": "カーソルAIは完全にリセットされ、試行検出がバイパスされました！", "warning_6": "このツールを実行した後、再びカーソルAIをセットアップする必要があります。", "confirm_5": "システム上の他のアプリケーションは影響を受けません。", "confirm_3": "コードファイルは影響を受けず、ツールは設計されています", "delete_input_not_found": "複数の試行後に確認されていない確認入力を削除します", "reset_cancelled": "リセットキャンセル。変更せずに終了します。"}, "updater": {"up_to_date": "最新バージョンを使用しています。", "update_skipped": "更新をスキップします。", "new_version_available": "利用可能な新しいバージョン！ （現在：{現在}、最新：{最新}）", "changelog_title": "Changelog", "updating": "最新バージョンへの更新。プログラムは自動的に再起動します。", "rate_limit_exceeded": "Github APIレート制限は超えました。更新チェックをスキップします。", "invalid_choice": "無効な選択。 「Y」または「n」を入力してください。", "development_version": "開発バージョン{current}> {最新}", "update_confirm": "最新バージョンに更新しますか？ （y/n）", "check_failed": "更新の確認に失敗しました：{エラー}", "continue_anyway": "現在のバージョンを続けています...", "checking": "更新のチェック..."}, "oauth": {"no_chrome_profiles_found": "デフォルトを使用して、Chromeプロファイルは見つかりませんでした", "found_browser_data_directory": "ブラウザデータディレクトリを見つけました：{path}", "github_start": "githubスタート", "using_browser_profile": "ブラウザプロファイルの使用：{プロファイル}", "waiting_for_authentication": "認証を待っています...", "error_finding_chrome_profile": "エラーChromeプロファイルの検索、デフォルトを使用：{エラー}", "browser_failed_to_start": "ブラウザが起動に失敗しました：{エラー}", "starting_browser": "ブラウザの起動：{パス}", "browser_setup_failed": "ブラウザのセットアップが失敗しました：{エラー}", "page_changed_checking_auth": "ページが変更され、認証を確認してください...", "user_data_dir_not_found": "{browser}ユーザーデータディレクトリ{path}で見つかりません、代わりにChromeを試してみます", "warning_browser_close": "警告：これにより、すべての実行中の{ブラウザー}プロセスが閉じられます", "initializing_browser_setup": "ブラウザのセットアップを初期化...", "redirecting_to_authenticator_cursor_sh": "Authenticator.cursor.shへのリダイレクト...", "missing_authentication_data": "認証データの欠落：{データ}", "browser_closed": "ブラウザは閉じました", "profile_selection_error": "プロフィール選択中のエラー：{エラー}", "found_cookies": "{count}クッキーが見つかりました", "authentication_failed": "認証が失敗した：{エラー}", "using_first_available_chrome_profile": "最初に利用可能なChromeプロファイルを使用する：{プロファイル}", "auth_update_failed": "AUTHアップデートは失敗しました", "failed_to_extract_auth_info": "認証情報の抽出に失敗しました：{エラー}", "status_check_error": "ステータスチェックエラー：{エラー}", "starting_new_google_authentication": "新しいGoogle認証を開始します...", "starting_re_authentication_process": "再認証プロセスを開始...", "could_not_find_usage_count": "使用量が見つかりませんでした：{エラー}", "running_as_root_warning": "ルートとして実行することは、ブラウザの自動化には推奨されません", "killing_browser_processes": "Killing {browser}プロセス...", "could_not_check_usage_count": "使用量カウントを確認できませんでした：{エラー}", "found_default_chrome_profile": "デフォルトのChromeプロファイルが見つかりました", "auth_update_success": "AUTHアップデートの成功", "authentication_successful": "認証成功 - 電子メール：{電子メール}", "browser_not_found_trying_chrome": "代わりにChromeを試して、{ブラウザー}を見つけることができませんでした", "starting_github_authentication": "GitHub認証を開始...", "please_select_your_google_account_to_continue": "Googleアカウントを選択して続行してください...", "warning_could_not_kill_existing_browser_processes": "警告：既存のブラウザプロセスを殺すことができませんでした：{エラー}", "browser_failed": "ブラウザが起動に失敗しました：{エラー}", "browser_failed_to_start_fallback": "ブラウザが起動に失敗しました：{エラー}", "authentication_timeout": "認証タイムアウト", "error_getting_user_data_directory": "ユーザーデータディレクトリの取得エラー：{エラー}", "using_configured_browser_path": "configured {browser}パスを使用：{path}", "could_not_find_email": "電子メールが見つかりませんでした：{エラー}", "starting_google_authentication": "Google認証を開始...", "google_start": "Google Start", "browser_setup_completed": "ブラウザのセットアップが正常に完了しました", "navigating_to_authentication_page": "認証ページへのナビゲート...", "invalid_authentication_type": "無効な認証タイプ", "try_running_without_sudo_admin": "Sudo/管理者の特権なしで実行してみてください", "authentication_successful_getting_account_info": "認証は成功し、アカウント情報を取得します...", "usage_count": "使用法数：{使用}", "failed_to_delete_account": "アカウントの削除に失敗しました：{エラー}", "already_on_settings_page": "すでに設定ページにあります！", "authentication_button_not_found": "認証ボタンが見つかりません", "consider_running_without_sudo": "Sudoなしでスクリプトを実行することを検討してください", "detected_platform": "検出されたプラットフォーム：{プラットフォーム}", "account_has_reached_maximum_usage": "アカウントは最大使用に達しました、{削除}", "found_email": "メールを見つけた：{電子メール}", "found_browser_user_data_dir": "見つかった{ブラウザー}ユーザーデータディレクトリ：{path}", "failed_to_delete_account_or_re_authenticate": "アカウントを削除したり、再認証したりできませんでした：{エラー}", "failed_to_delete_expired_account": "期限切れのアカウントを削除できませんでした", "token_extraction_error": "トークン抽出エラー：{エラー}", "supported_browsers": "{プラットフォーム}のサポートブラウザ", "found_chrome_at": "クロムを見つけました：{path}", "starting_new_authentication_process": "新しい認証プロセスを開始...", "no_compatible_browser_found": "互換性のあるブラウザは見つかりません。 Google ChromeまたはChromiumをインストールしてください。", "account_is_still_valid": "アカウントはまだ有効です（使用法：{使用}）"}, "github_register": {"invalid_choice": "無効な選択。 「はい」または「いいえ」を入力してください", "feature6": "すべての資格情報をファイルに保存します。", "warning3": "Captchaまたは追加の検証は、自動化を中断する可能性があります。", "starting_automation": "開始自動化...", "feature1": "1セックスを使用して一時的な電子メールを生成します。", "credentials_saved": "これらの資格情報は、github_cursor_accounts.txtに保存されています", "warning1": "このスクリプトは、GitHub/Cursorのサービス条件に違反する可能性があるアカウント作成を自動化します。", "completed_successfully": "GitHub +カーソル登録は正常に完了しました！", "program_terminated": "ユーザーによって終了したプログラム", "title": "Github + Cursor AI登録自動化", "email_address": "電子メールアドレス", "feature3": "GitHubメールを自動的に検証します。", "feature2": "ランダムな資格情報を使用して、新しいGitHubアカウントを登録します。", "features_header": "特徴", "warning4": "責任を持って自分の責任で使用してください。", "github_username": "githubユーザー名", "feature5": "トライアル検出をバイパスするために、マシンIDをリセットします。", "check_browser_windows_for_manual_intervention_or_try_again_later": "手動介入についてはブラウザのウィンドウを確認するか、後でもう一度やり直してください。", "github_password": "githubパスワード", "warning2": "インターネットアクセスと管理特権が必要です。", "confirm": "先に進みたいですか？", "registration_encountered_issues": "Gith<PERSON> +カーソル登録に問題が発生しました。", "cancelled": "操作はキャンセルされました", "feature4": "GitHub認証を使用して、カーソルAIにログインします。", "warnings_header": "警告"}, "account_delete": {"confirm_button_not_found": "複数の試行後に見つかっていない確認ボタンを確認してください", "typed_delete": "確認ボックスに「削除」と入力されました", "error": "アカウントの削除中のエラー：{エラー}", "delete_input_retry": "入力が見つかっていない削除、{pirte}/{max_attempts}", "starting_process": "アカウントの削除プロセスを開始...", "advanced_tab_error": "ERRORINSING ADVANCED TAB：{エラー}", "delete_button_clicked": "[アカウントの削除]ボタンをクリックしました", "auth_timeout": "認証タイムアウト、とにかく続行...", "logging_in": "Googleでログインしています...", "login_redirect_failed": "ログインリダイレクトが失敗し、直接ナビゲーションを試みます...", "already_on_settings": "すでに設定ページにあります", "delete_button_not_found": "複数の試行後にアカウントボタンを削除しません", "delete_button_retry": "削除ボタンが見つかりません、{pirte}/{max_attempts}を削除します}", "trying_settings": "[設定]ページに移動しようとしています...", "delete_input_not_found_continuing": "とにかく続行しようとしている確認の入力を削除します", "interrupted": "ユーザーが中断したアカウント削除プロセス。", "email_not_found": "電子メールが見つかりません：{エラー}", "delete_input_not_found": "複数の試行後に確認されていない確認入力を削除します", "title": "カーソルGoogleアカウント削除ツール", "waiting_for_auth": "Google認証を待っています...", "google_button_not_found": "Googleログインボタンが見つかりません", "navigating_to_settings": "[設定]ページへのナビゲート...", "advanced_tab_clicked": "[詳細]タブをクリックしました", "advanced_tab_not_found": "複数の試行後には高度なタブが見つかりません", "cancelled": "アカウントの削除がキャンセルされました。", "account_deleted": "アカウントが正常に削除されました！", "warning": "警告：これにより、カーソルアカウントが永久に削除されます。このアクションを元に戻すことはできません。", "delete_button_error": "エラー検索削除ボタン：{エラー}", "delete_input_error": "ERRORの検索入力の削除：{エラー}", "found_danger_zone": "Danger Zoneセクションが見つかりました", "confirm_button_error": "エラー検索確認ボタン：{エラー}", "unexpected_error": "予期しないエラー：{エラー}", "unexpected_page": "ログイン後の予期しないページ：{url}", "confirm_prompt": "先に進みたいですか？ （y/n）：", "found_email": "メールを見つけた：{電子メール}", "login_successful": "ログインが成功します", "direct_advanced_navigation": "[Advanced Tab]に直接ナビゲーションを試します", "advanced_tab_retry": "高度なタブが見つかりません、試み{pirter}/{max_attempts}", "select_google_account": "Googleアカウントを選択してください...", "confirm_button_retry": "確認ボタンが見つかりません、{pirte}/{max_attempts}を確認します", "failed": "アカウントの削除プロセスが失敗するか、キャンセルされました。", "success": "カーソルアカウントが正常に削除されました！"}, "browser_profile": {"default_profile": "デフォルトのプロファイル", "profile_selected": "選択したプロファイル：{プロフィール}", "title": "ブラウザプロファイルの選択", "invalid_selection": "無効な選択。もう一度やり直してください。", "profile_list": "利用可能{ブラウザー}プロファイル：", "error_loading": "エラーロード{ブラウザー}プロファイル：{エラー}", "profile": "プロフィール{番号}", "select_profile": "使用する{browser}プロファイルを選択します。", "no_profiles": "{ブラウザー}プロファイルは見つかりません"}, "account_info": {"active": "アクティブ", "team": "チーム", "premium": "プレミアム", "inactive": "非アクティブ", "failed_to_get_account": "アカウント情報を取得できませんでした", "lifetime_access_enabled": "Lifetimeアクセスが有効になっています", "subscription_not_found": "サブスクリプション情報が見つかりません", "usage": "使用法", "failed_to_get_account_info": "アカウント情報を取得できませんでした", "failed_to_get_token": "トークンを取得できませんでした", "basic_usage": "基本的な使用法", "failed_to_get_usage": "使用情報を取得できませんでした", "subscription_type": "サブスクリプションタイプ", "subscription": "サブスクリプション", "trial_remaining": "残りのプロトライアル", "token_not_found": "トークンが見つかりません", "free": "無料", "pro": "プロ", "token": "トークン", "days_remaining": "残りの日", "usage_not_found": "使用は見つかりません", "failed_to_get_subscription": "サブスクリプション情報を取得できませんでした", "email": "メール", "enterprise": "企業", "failed_to_get_email": "メールアドレスを取得できませんでした", "pro_trial": "プロトライアル", "days": "日", "remaining_trial": "残りの試験", "title": "アカウント情報", "email_not_found": "電子メールが見つかりません", "config_not_found": "構成が見つかりません。", "premium_usage": "プレミアム使用"}, "reset": {"version_check_passed": "カーソルバージョンチェックが合格しました", "reading_package_json": "Package.jsonを読む{パス}", "no_write_permission": "書き込み許可なし：{パス}", "invalid_json_object": "無効なJSONオブジェクト", "update_success": "成功を更新します", "creating_backup": "構成バックアップの作成", "updating_pair": "キー価値ペアの更新", "system_ids_updated": "システムIDは正常に更新されました", "version_field_empty": "バージョンフィールドは空です", "sqlite_error": "sqliteデータベースの更新に失敗した：{エラー}", "update_windows_machine_guid_failed": "Windows Machine Guidの更新失敗：{エラー}", "update_windows_machine_id_failed": "WindowsマシンIDの更新失敗：{エラー}", "windows_permission_denied": "Windowsの許可は拒否されました", "start_patching": "GetMachineIDのパッチを開始します", "run_as_admin": "このプログラムを管理者として実行してみてください", "permission_denied": "許可拒否：{エラー}", "system_ids_update_failed": "システムIDの更新が失敗しました：{エラー}", "invalid_version_format": "無効なバージョン形式：{バージョン}", "backup_exists": "バックアップファイルはすでに存在し、バックアップステップをスキップします", "title": "カーソルマシンIDリセットツール", "file_modified": "ファイルが変更されました", "reading": "現在の構成を読み取ります", "macos_uuid_update_failed": "MacOS UUIDアップデートは失敗しました", "windows_guid_update_failed": "Windows GUIDの更新に失敗しました", "update_failed": "更新が失敗しました：{エラー}", "no_permission": "構成ファイルの読み取りまたは書き込みはできません。ファイル許可を確認してください", "patch_completed": "GetMachineIDが完了しました", "detecting_version": "カーソルバージョンの検出", "generating": "新しいマシンIDの生成", "version_greater_than_0_45": "カーソルバージョン> = 0.45.0、getMachineIDのパッチング", "path_not_found": "パスが見つかりません：{パス}", "process_error": "プロセスエラーのリセット：{エラー}", "version_too_low": "カーソルバージョンが低すぎる：{バージョン} <0.45.0", "patching_getmachineid": "getMachineidのパッチング", "sqlite_success": "SQLiteデータベースは正常に更新されました", "stack_trace": "スタックトレース", "patch_failed": "getMachineIDが失敗したパッチング：{エラー}", "permission_error": "許可エラー：{エラー}", "updating_system_ids": "システムIDの更新", "press_enter": "Enterを押して終了します", "updating_sqlite": "SQLiteデータベースの更新", "windows_machine_guid_updated": "Windows Machine GUIDは正常に更新されました", "windows_machine_id_updated": "Windows Machine IDは正常に更新されました", "backup_created": "作成されたバックアップ", "current_version": "現在のカーソルバージョン：{バージョン}", "unsupported_os": "サポートされていないOS：{os}", "success": "マシンIDが正常にリセットされます", "package_not_found": "package.jsonが見つかりません：{path}", "plutil_command_failed": "Plutilコマンドが失敗しました", "not_found": "構成ファイルが見つかりません", "found_version": "見つかったバージョン：{バージョン}", "linux_path_not_found": "Linuxパスが見つかりません", "version_parse_error": "バージョン解析エラー：{エラー}", "macos_uuid_updated": "MacOS UUIDは正常に更新されました", "new_id": "新しいマシンID", "checking": "構成ファイルの確認", "no_version_field": "package.jsonにあるバージョンフィールドはありません", "check_version_failed": "バージョンに失敗したチェック：{エラー}", "file_not_found": "ファイルが見つかりません：{path}", "modify_file_failed": "ファイルに失敗した変更：{エラー}", "saving_json": "JSONに新しい構成を保存します", "windows_guid_updated": "Windows GUIDは正常に更新されました", "version_less_than_0_45": "カーソルバージョン<0.45.0、getMachineidのパッチをスキップします"}, "auth_check": {"user_unauthorized": "ユーザーは不正です", "jwt_token_warning": "トークンはJWT形式のように見えますが、APIチェックは予期しないステータスコードを返しました。トークンは有効かもしれませんが、APIアクセスは制限されています。", "error_generating_checksum": "エラーチェックサムの生成：{エラー}", "operation_cancelled": "操作はユーザーによってキャンセルされました", "token_source": "データベースからトークンを取得するか、手動で入力しますか？ （D/M、デフォルト：D）", "user_authorized": "ユーザーは承認されています", "checking_authorization": "認可を確認...", "token_length": "トークンの長さ：{長さ}文字", "connection_error": "接続エラー", "token_found_in_db": "データベースにあるトークン", "check_error": "エラーチェック承認：{エラー}", "usage_response_status": "使用率応答ステータス：{応答}", "check_usage_response": "使用状況の応答を確認する：{応答}", "request_timeout": "リクエストがタイムアウトします", "unexpected_error": "予期しないエラー：{エラー}", "enter_token": "カーソルトークンを入力してください：", "invalid_token": "無効なトークン", "cursor_acc_info_not_found": "cursor_acc_info.pyが見つかりません", "authorization_failed": "承認は失敗しました！", "usage_response": "使用率：{応答}", "getting_token_from_db": "データベースからトークンを取得します...", "unexpected_status_code": "予期しないステータスコード：{code}", "checking_usage_information": "使用情報の確認...", "authorization_successful": "承認は成功しました！", "error_getting_token_from_db": "データベースからトークンの取得エラー：{エラー}", "token_not_found_in_db": "データベースにはありません"}, "update": {"disable_failed": "自動更新の無効化失敗：{エラー}", "block_file_locked_error": "ブロックファイルロックされたエラー：{エラー}", "yml_locked_error": "update.ymlファイルロックされたエラー：{エラー}", "press_enter": "Enterを押して終了します", "block_file_locked": "ブロックファイルがロックされています", "removing_directory": "ディレクトリの削除", "update_yml_not_found": "update.ymlファイルが見つかりません", "directory_locked": "ディレクトリがロックされています：{path}", "yml_already_locked_error": "update.ymlファイル既にロックされたエラー：{エラー}", "remove_directory_failed": "ディレクトリの削除に失敗しました：{エラー}", "unsupported_os": "サポートされていないOS：{システム}", "block_file_already_locked_error": "ブロックファイル既にロックされたエラー：{エラー}", "yml_locked": "update.ymlファイルがロックされています", "yml_already_locked": "update.ymlファイルはすでにロックされています", "start_disable": "自動更新の無効化を開始します", "block_file_created": "作成されたブロックファイル", "create_block_file_failed": "ブロックファイルの作成に失敗しました：{エラー}", "directory_removed": "ディレクトリが削除されました", "clearing_update_yml": "update.ymlファイルのクリア", "title": "Cursor Auto Updateを無効にします", "processes_killed": "殺されたプロセス", "creating_block_file": "ブロックファイルの作成", "killing_processes": "殺害プロセス", "disable_success": "自動更新は無効になっています", "block_file_already_locked": "ブロックファイルはすでにロックされています", "update_yml_cleared": "update.ymlファイルがクリアされました", "clear_update_yml_failed": "update.ymlファイルをクリアできなかった：{エラー}"}, "token": {"refresh_success": "トークンは正常にリフレッシュしました！ {Days} Days（期限切れ：{期限切れ}）に有効", "extraction_error": "トークンの抽出エラー：{エラー}", "server_error": "サーバーエラーの更新：http {status}", "unexpected_error": "トークン更新中の予期しないエラー：{エラー}", "connection_error": "サーバーを更新するための接続エラー", "refreshing": "リフレッシュトークン...", "no_access_token": "それに応じてアクセストークンはありません", "invalid_response": "更新サーバーからの無効なJSON応答", "refresh_failed": "トークンの更新失敗：{エラー}", "request_timeout": "タイミングでサーバーを更新するリクエスト"}, "email": {"try_export_display": "試してください：display =：0をエクスポートします", "create_failed": "メールの作成に失敗しました", "domains_list_error": "ドメインリストの取得に失敗しました：{エラー}", "domains_excluded": "ドメインを除外する：{ドメイン}", "blocked_domains_loaded_success": "ブロックされたドメインが正常にロードされました", "no_available_domains_after_filtering": "フィルタリング後の利用可能なドメインはありません", "make_sure_chrome_chromium_is_properly_installed": "Chrome/Chromiumが適切に取り付けられていることを確認してください", "visiting_site": "メールドメインにアクセスします", "create_success": "電子メールが正常に作成されました", "trying_to_create_email": "電子メールを作成しようとしています：{電子メール}", "starting_browser": "起動ブラウザ", "refresh_error": "電子メールの更新エラー：{エラー}", "refresh_button_not_found": "更新ボタンが見つかりません", "blocked_domains_loaded_timeout_error": "ブロックされたドメインロードされたタイムアウトエラー：{エラー}", "verification_error": "検証エラー：{エラー}", "verification_found": "検証が見つかりました", "refreshing": "更新されたメール", "verification_code_found": "検証コードが見つかりました", "extension_load_error": "拡張ロードエラー：{エラー}", "domains_filtered": "フィルタリングされたドメイン：{count}", "blocked_domains_loaded_timeout": "ブロックされたドメインがロードされたタイムアウト：{タイムアウト} s", "address": "電子メールアドレス", "all_domains_blocked": "すべてのドメインがブロックされ、スイッチングサービス", "blocked_domains_loaded": "ロードされたブロックドメイン：{count}", "account_creation_error": "アカウント作成エラー：{エラー}", "create_error": "電子メールの作成エラー：{エラー}", "verification_code_error": "検証コードエラー：{エラー}", "switching_service": "{Service}サービスに切り替えます", "failed_to_create_account": "アカウントの作成に失敗しました", "blocked_domains_loaded_error": "ブロックされたドメインロードエラー：{エラー}", "no_display_found": "ディスプレイは見つかりません。 Xサーバーが実行されていることを確認してください。", "verification_code_not_found": "検証コードが見つかりません", "refresh_success": "電子メールが正常に更新されました", "using_chrome_profile": "クロムプロファイルの使用：{user_data_dir}", "failed_to_get_available_domains": "利用可能なドメインを取得できませんでした", "blocked_domains": "ブロックされたドメイン：{ドメイン}", "verification_not_found": "検証が見つかりません", "available_domains_loaded": "使用可能なドメインロード：{count}", "try_install_chromium": "試してください：sudo apt install chrom-browser", "domain_blocked": "ブロックされたドメイン：{ドメイン}"}, "control": {"verification_not_found": "検証コードは見つかりません", "found_verification_code": "検証コードが見つかりました", "email_copy_error": "電子メールコピーエラー：{エラー}", "generate_email_success": "電子メールの成功を生成します", "copy_email": "メールアドレスのコピー", "database_connection_closed": "データベース接続が閉じられました", "navigate_to": "{url}に移動する", "enter_mailbox_success": "メールボックスの成功を入力します", "check_verification": "確認コードの確認", "get_cursor_session_token_success": "カーソルセッショントークンの成功を取得します", "navigation_error": "ナビゲーションエラー：{エラー}", "database_updated_successfully": "データベースは正常に更新されました", "token_saved_to_file": "cursor_tokens.txtに保存されたトークン", "mailbox_error": "メールボックスエラー：{エラー}", "no_valid_verification_code": "有効な検証コードはありません", "save_token_failed": "保存トークンが失敗しました", "get_cursor_session_token": "カーソルセッショントークンを取得します", "enter_mailbox": "メールボックスを入力します", "get_email_address_success": "メールアドレスの成功を取得します", "get_email_name": "メール名を取得します", "blocked_domain": "ブロックドメイン", "get_email_address": "メールアドレスを取得します", "refresh_mailbox": "更新されたメールボックス", "get_cursor_session_token_failed": "カーソルセッショントークンを取得しました", "browser_error": "ブラウザ制御エラー：{エラー}", "select_domain": "ランダムドメインの選択", "select_email_domain": "電子メールドメインを選択します", "select_email_domain_success": "電子メールドメインの成功を選択します", "verification_found": "検証コードが見つかりました", "get_email_name_success": "電子メール名の成功を取得します", "generate_email": "新しい電子メールの生成"}, "bypass": {"current_version": "現在のバージョン：{バージョン}", "title": "カーソルバージョンバイパスツール", "no_write_permission": "ファイルの書き込み許可はありません：{path}", "write_failed": "Product.jsonの書き込みに失敗しました：{エラー}", "menu_option": "バイパスカーソルバージョンチェック", "bypass_failed": "バージョンバイパスが失敗しました：{エラー}", "starting": "カーソルバージョンバイパスを開始...", "read_failed": "crods.jsonの読みに失敗しました：{エラー}", "unsupported_os": "サポートされていないオペレーティングシステム：{システム}", "description": "このツールは、cursorのcrods.jsonをバージョンの制限をバイパスするように変更します", "product_json_not_found": "Product.jsonは、一般的なLinuxパスにはありません", "version_updated": "{old}から{new}に更新されたバージョン", "no_update_needed": "更新は必要ありません。現在のバージョン{バージョン}はすでに> = 0.46.0です", "file_not_found": "ファイルが見つかりません：{path}", "backup_created": "作成されたバックアップ：{パス}", "found_product_json": "FOUND PRODUCT.JSON：{PATH}", "stack_trace": "スタックトレース", "localappdata_not_found": "LocalAppData環境変数が見つかりません"}, "quit_cursor": {"start": "カーソルの終了を開始します", "terminating": "終了プロセス{pid}", "error": "エラーが発生しました：{エラー}", "waiting": "プロセスが終了するのを待っています", "no_process": "実行中のカーソルプロセスはありません", "timeout": "プロセスタイムアウト：{pids}", "success": "すべてのカーソルプロセスは閉じられました"}, "auth": {"press_enter": "Enterを押して終了します", "auth_file_create_failed": "AUTHファイルの作成失敗：{エラー}", "database_updated_successfully": "データベースは正常に更新されました", "db_connection_error": "データベースへの接続に失敗しました：{エラー}", "updating_auth": "認証情報の更新", "auth_update_failed": "認証情報の更新失敗：{エラー}", "connected_to_database": "データベースに接続されています", "title": "カーソル認証マネージャー", "updating_pair": "キー価値ペアの更新", "database_connection_closed": "データベース接続が閉じられました", "reading_auth": "認証ファイルを読み取ります", "db_not_found": "データベースファイルが見つかりません：{path}", "auth_updated": "AUTH情報は正常に更新されました", "auth_not_found": "認証ファイルが見つかりません", "checking_auth": "認証ファイルの確認", "auth_file_error": "AUTHファイルエラー：{エラー}", "db_permission_error": "データベースファイルにアクセスできません。許可を確認してください", "auth_file_created": "作成されたAUTHファイル", "reset_machine_id": "マシンIDをリセットします"}, "bypass_token_limit": {"title": "バイパストークン制限ツール", "press_enter": "Enterを押して続行します...", "description": "このツールは、workbench.desktop.main.jsファイルを変更して、トークン制限をバイパスします"}, "manual_auth": {"proceed_prompt": "進む？ （y/n）：", "auth_updated_successfully": "認証情報が正常に更新されました！", "token_verification_skipped": "トークン検証がスキップされた（check_user_authorized.pyが見つかりません）", "token_required": "トークンが必要です", "auth_type_selected": "選択した認証タイプ：{タイプ}", "auth_type_google": "グーグル", "continue_anyway": "とにかく続行しますか？ （y/n）：", "auth_type_github": "github", "verifying_token": "トークンの妥当性の確認...", "auth_type_prompt": "認証タイプを選択します。", "error": "エラー：{エラー}", "random_email_generated": "生成されたランダムメール：{email}", "operation_cancelled": "操作はキャンセルされました", "auth_type_auth0": "auth_0（デフォルト）", "email_prompt": "電子メールを入力します（ランダム電子メールのために空白のままにしてください）：", "token_verification_error": "トークンの検証エラー：{エラー}", "token_prompt": "カーソルトークン（access_token/refresh_token）を入力してください：", "token_verified": "トークンは正常に検証されました！", "confirm_prompt": "次の情報を確認してください。", "invalid_token": "無効なトークン。認証が中止されました。", "title": "手動カーソル認証", "updating_database": "カーソル認証データベースの更新...", "auth_update_failed": "認証情報の更新に失敗しました"}, "tempmail": {"general_error": "エラーが発生しました：{エラー}", "no_email": "カーソル検証メールは見つかりません", "config_error": "構成ファイルエラー：{エラー}", "checking_email": "カーソル検証メールの確認...", "extract_code_failed": "検証コードが失敗した抽出：{エラー}", "configured_email": "構成された電子メール：{電子メール}", "email_found": "カーソル検証メールが見つかりました", "no_code": "確認コードを取得できませんでした", "check_email_failed": "電子メールの失敗を確認する：{エラー}", "verification_code": "検証コード：{code}"}}