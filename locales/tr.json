{"menu": {"title": "<PERSON><PERSON><PERSON>", "exit": "<PERSON><PERSON>", "reset": "<PERSON><PERSON><PERSON>", "register": "Yeni Cursor He<PERSON>", "register_google": "Google Hesabı ile Kayıt Ol", "register_github": "GitHub Hesabı ile Kayıt Ol", "register_manual": "Cursor'ı Özel E-posta ile <PERSON>", "quit": "Cursor Uygulamasını Kapat", "select_language": "<PERSON><PERSON>", "select_chrome_profile": "Chrome Profilini Seç", "input_choice": "Lütfen seçiminizi girin ({choices})", "invalid_choice": "Geçersiz seçim. Lütfen {choices} arasından bir sayı girin", "program_terminated": "Program kullanıcı tarafından sonlandırıldı", "error_occurred": "<PERSON>ir hata o<PERSON>: {error}. Lütfen tekrar deneyin", "press_enter": "Çıkmak için Enter'a Basın", "disable_auto_update": "Cursor Otomatik Güncellemeyi Devre Dışı Bırak", "lifetime_access_enabled": "ÖMÜR BOYU ERİŞİM ETKİNLEŞTİRİLDİ", "totally_reset": "Cursor'ı Tamamen Sıfırla", "outdate": "<PERSON><PERSON><PERSON><PERSON>", "temp_github_register": "Geçici GitHub Kaydı", "admin_required": "Running as executable, administrator privileges required.", "admin_required_continue": "Continuing without administrator privileges.", "coming_soon": "Yakında", "fixed_soon": "Yakında <PERSON>k", "contribute": "Projeye Katkıda Bulun", "config": "Yapılandırmayı Göster", "delete_google_account": "Cursor Google Hesabını Sil", "continue_prompt": "<PERSON>am et? (y/N): ", "operation_cancelled_by_user": "İşlem kullanıcı tarafından iptal edildi", "exiting": "Çıkılıyor ......", "bypass_version_check": "<PERSON><PERSON><PERSON>ü<PERSON><PERSON><PERSON>", "check_user_authorized": "Kullanıcı Yetkilendirmesini Kontrol Et", "bypass_token_limit": "Token Limiti<PERSON>", "restore_machine_id": "<PERSON><PERSON><PERSON> G<PERSON>", "language_config_saved": "Dil yapılandırması başarıyla kaydedildi", "lang_invalid_choice": "Geçersiz seçim. Lütfen aşağıdaki seçeneklerden birini girin: ({Lang_choices}))", "manual_custom_auth": "<PERSON>"}, "languages": {"ar": "<PERSON><PERSON><PERSON>", "en": "English", "zh_cn": "简体中文", "zh_tw": "繁體中文", "vi": "Vietnamese", "nl": "Dutch", "de": "German", "fr": "French", "pt": "Portuguese", "ru": "Russian", "tr": "Turkish", "es": "Spanish", "bg": "Bulgarca", "ja": "Japonca", "it": "<PERSON><PERSON><PERSON>"}, "quit_cursor": {"start": "Cursor<PERSON><PERSON><PERSON> Başlatılıyor", "no_process": "Çalışan Cursor İşlemi Yok", "terminating": "İşlem Sonlandırılıyor {pid}", "waiting": "İşlemin Çıkması Bekleniyor", "success": "Tüm Cursor İşlemleri Kapatıldı", "timeout": "İşlem Zaman Aşımı: {pids}", "error": "<PERSON><PERSON>: {error}"}, "reset": {"title": "<PERSON><PERSON>or Ma<PERSON>i Sıfırlama Aracı", "checking": "Yapılandırma Dosyası Kontrol Ediliyor", "not_found": "Yapılandırma Dosyası Bulunamadı", "no_permission": "Yapılandırma Dosyası Okunamıyor veya Yazılamıyor, Lütfen Dosya İzinlerini Kontrol Edin", "reading": "Mevcut <PERSON>", "creating_backup": "Yapılandırma Yedeği Oluşturuluyor", "backup_exists": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> Atlanıyor", "generating": "<PERSON><PERSON> Ma<PERSON>e <PERSON>ği Oluşturuluyor", "saving_json": "Yeni <PERSON>landırma JSON'a Kaydediliyor", "success": "<PERSON><PERSON><PERSON> Kimliği Başarıyla Sıfırlandı", "new_id": "<PERSON><PERSON>", "permission_error": "<PERSON><PERSON> Hatası: {error}", "run_as_admin": "Lütfen Bu Programı Yönetici Olarak Çalıştırmayı Deneyin", "process_error": "Sıfırlama İşlemi Hatası: {error}", "updating_sqlite": "SQLite Veritabanı Güncelleniyor", "updating_pair": "Anahtar-<PERSON><PERSON><PERSON> Çifti Güncelleniyor", "sqlite_success": "SQLite Veritabanı Başarıyla Güncellendi", "sqlite_error": "SQLite Veritabanı Güncellemesi Başarısız: {error}", "press_enter": "Çıkmak için Enter'a Basın", "unsupported_os": "Desteklenmeyen İşletim Sistemi: {os}", "linux_path_not_found": "Linux Yolu Bulunamadı", "updating_system_ids": "Sistem Kimlikleri Güncelleniyor", "system_ids_updated": "Sistem Kimlikleri Başarıyla Güncellendi", "system_ids_update_failed": "Sistem Kimlikleri Güncellemesi Başarısız: {error}", "windows_guid_updated": "Windows GUID Başarıyla Güncellendi", "windows_permission_denied": "Windows İzni Reddedildi", "windows_guid_update_failed": "Windows GUID Güncellemesi Başarısız", "macos_uuid_updated": "macOS UUID Başarıyla Güncellendi", "plutil_command_failed": "plutil Komutu Başarısız", "start_patching": "getMachineId Yamalanması Başlatılıyor", "macos_uuid_update_failed": "macOS UUID Güncellemesi Başarısız", "current_version": "Mevcut Cursor <PERSON>: {version}", "patch_completed": "getMachineId Yamalama Tamamlandı", "patch_failed": "getMachineId Yamalama Başarısız: {error}", "version_check_passed": "Cursor Sürüm Kontrolü Geçildi", "file_modified": "<PERSON><PERSON><PERSON>", "version_less_than_0_45": "<PERSON>ursor <PERSON>ürümü < 0.45.0, getMachineId Yamalama Atlanıyor", "detecting_version": "Cursor Sürümü Tespit Ediliyor", "patching_getmachineid": "getMachineId Yamalanıyor", "version_greater_than_0_45": "<PERSON><PERSON>or <PERSON>ü<PERSON> >= 0.45.0, getMachineId Yamalanıyor", "permission_denied": "<PERSON><PERSON> Reddedildi: {error}", "backup_created": "<PERSON><PERSON>", "update_success": "<PERSON><PERSON><PERSON><PERSON><PERSON>arılı", "update_failed": "<PERSON><PERSON><PERSON><PERSON><PERSON>ı<PERSON>ız: {error}", "windows_machine_guid_updated": "Windows Makine GUID'si Başarıyla Güncellendi", "reading_package_json": "package.j<PERSON> {path}", "invalid_json_object": "Geçersiz JSON Nesnesi", "no_version_field": "package.json İçinde Sürüm Alanı Bulunamadı", "version_field_empty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "invalid_version_format": "Geçersiz Sürüm Formatı: {version}", "found_version": "<PERSON><PERSON><PERSON><PERSON><PERSON> Bulundu: {version}", "version_parse_error": "<PERSON><PERSON><PERSON><PERSON><PERSON> Ayrıştırma Hatası: {error}", "package_not_found": "Package.json Bulunamadı: {path}", "check_version_failed": "<PERSON><PERSON><PERSON><PERSON>m Kontrolü Başarısız: {error}", "stack_trace": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "version_too_low": "Cursor Sürümü Çok Düşük: {version} < 0.45.0", "update_windows_machine_id_failed": "Windows Machine Kimliğini Güncelle Başarısız: {Hata}", "windows_machine_id_updated": "Windows Machine Kimliği başar<PERSON><PERSON> gü<PERSON>llendi", "path_not_found": "Yol bulunamadı: {yol}", "update_windows_machine_guid_failed": "Windows Machine Guid'i gü<PERSON><PERSON>yin <PERSON>arısız: {hata}", "no_write_permission": "Yazma İzni Yok: {Path}", "file_not_found": "<PERSON><PERSON><PERSON> bulunamad<PERSON>: {yol}", "modify_file_failed": "Dosyayı Değerlendirme Başarısız: {Hata}"}, "register": {"title": "Cursor <PERSON>", "start": "Kayıt işlemi başlatılıyor...", "handling_turnstile": "Güvenlik doğrulaması işleniyor...", "retry_verification": "Doğrulama tekrar deneniyor...", "detect_turnstile": "Güvenlik doğrulaması kontrol ediliyor...", "verification_success": "Güvenlik doğrulaması başarılı", "starting_browser": "Tarayıcı açılıyor...", "form_success": "Form başarıyla gönderildi", "browser_started": "Tarayıcı başarıyla açıldı", "waiting_for_second_verification": "E-posta doğrulaması bekleniyor...", "waiting_for_verification_code": "Doğrulama kodu bekleniyor...", "password_success": "<PERSON><PERSON><PERSON> başarıyla ayarlandı", "password_error": "<PERSON><PERSON><PERSON>: {error}. Lütfen tekrar deneyin", "waiting_for_page_load": "<PERSON><PERSON>...", "first_verification_passed": "İlk doğrulama başarılı", "mailbox": "E-posta gelen kutusuna başarıyla erişildi", "register_start": "<PERSON><PERSON><PERSON>", "form_submitted": "Form Gönderildi, Doğrulama Başlatılıyor...", "filling_form": "Form Dolduruluyor", "visiting_url": "URL Ziyaret <PERSON>", "basic_info": "Temel Bilgiler Gönderildi", "handle_turnstile": "Turnstile İşleniyor", "no_turnstile": "Turnstile <PERSON>", "turnstile_passed": "<PERSON><PERSON><PERSON> Geçildi", "verification_start": "Doğrulama Kodu Alma Başlatılıyor", "verification_timeout": "Doğrulama Kodu Alma Zaman Aşımı", "verification_not_found": "Doğrulama Kodu Bulunamadı", "try_get_code": "Deneme | {attempt} Doğrulama Kodu Al | Kalan Süre: {time}s", "get_account": "Hesap Bilgileri Alınıyor", "get_token": "Cursor Oturum Jetonu Alınıyor", "token_success": "Jeton Başarıyla Alındı", "token_attempt": "Deneme | {attempt} kez <PERSON>on alma denemesi | {time}s i<PERSON><PERSON>e tekrar den<PERSON>k", "token_max_attempts": "Maks<PERSON><PERSON> Deneme Sayısına Ulaşıldı ({max}) | Jeton alınamadı", "token_failed": "Jeton Alma Başarısız: {error}", "account_error": "<PERSON><PERSON><PERSON> Bilgisi Alma Başarısız: {error}", "press_enter": "Çıkmak için Enter'a Basın", "browser_start": "Tarayıcı Başlatılıyor", "open_mailbox": "Posta Kutusu Sayfası Açılıyor", "email_error": "E-posta Adresi Alınamadı", "setup_error": "E-posta Kuru<PERSON>: {error}", "start_getting_verification_code": "Doğrulama Kodu Alma Başlatılıyor, 60 saniye içinde denenecek", "get_verification_code_timeout": "Doğrulama Kodu Alma Zaman Aşımı", "get_verification_code_success": "Doğrulama Kodu Alma Başarılı", "try_get_verification_code": "Deneme | {attempt} Doğrulama Kodu Al | Kalan Süre: {remaining_time}s", "verification_code_filled": "Doğrulama Kodu Dolduruldu", "login_success_and_jump_to_settings_page": "Giriş Başarılı ve Ayarlar Sayfasına Yönlendiriliyor", "detect_login_page": "<PERSON><PERSON><PERSON> Algılandı, G<PERSON>ş Başlatılıyor...", "cursor_registration_completed": "Cursor Kaydı Tamamlandı!", "set_password": "<PERSON><PERSON><PERSON>", "basic_info_submitted": "Temel Bilgiler Gönderildi", "cursor_auth_info_updated": "<PERSON><PERSON><PERSON> Bilgileri Güncellendi", "cursor_auth_info_update_failed": "Cursor Kimlik Bilgileri Güncellemesi Başarısız", "reset_machine_id": "<PERSON><PERSON><PERSON>", "account_info_saved": "<PERSON><PERSON><PERSON>", "save_account_info_failed": "Hesap Bilgilerini Kaydetme Başarısız", "get_email_address": "E-posta Adresi Al", "update_cursor_auth_info": "<PERSON><PERSON><PERSON>ilgilerini Güncelle", "register_process_error": "<PERSON><PERSON><PERSON> İşlemi Hatası: {error}", "setting_password": "<PERSON><PERSON><PERSON>", "manual_code_input": "<PERSON>", "manual_email_input": "<PERSON>", "password": "Şifre", "first_name": "Ad", "last_name": "Soyad", "exit_signal": "Çıkış Sinyali", "email_address": "E-posta Adresi", "config_created": "Yapılandırma O<PERSON>şturuldu", "verification_failed": "Doğrulama Başarısız", "verification_error": "Doğrulama Hatası: {error}", "config_option_added": "Yapılandırma Seçeneği Eklendi: {option}", "config_updated": "Yapılandırma <PERSON>", "password_submitted": "<PERSON><PERSON><PERSON>", "total_usage": "Toplam Kullanım: {usage}", "setting_on_password": "<PERSON><PERSON><PERSON>", "getting_code": "Doğ<PERSON><PERSON><PERSON> Alınıyor, 60 saniye içinde denenecek", "human_verify_error": "Kullanıcının insan olduğu doğrulanamıyor. Tekrar deneniyor...", "max_retries_reached": "<PERSON>ks<PERSON><PERSON> deneme sayısına ulaşıldı. <PERSON><PERSON><PERSON> başarısı<PERSON>.", "using_browser": "{Tarayıcı} ta<PERSON>ıcı kullanma: {yol}", "could_not_track_processes": "{Tarayıc<PERSON>} işlemleri izlemedi: {hata}", "try_install_browser": "Tarayıcıyı paket yöneticinizle yüklemeyi deneyin", "tempmail_plus_verification_started": "Başlangıç ​​TempmailPlus doğrulama işlemi", "tempmail_plus_enabled": "Tempmailplus etkinleştirildi", "browser_path_invalid": "{tarayıcı} yolu geç<PERSON>iz, var<PERSON><PERSON>lan yolu kullanılarak", "using_tempmail_plus": "E -posta doğrulaması için tempmailplus kullanma", "tracking_processes": "İzleme {Count} {tarayıcı} işlemleri", "tempmail_plus_epin_missing": "Tempmailplus epin yapılandırılmamış", "tempmail_plus_verification_failed": "Tempmailplus doğrulaması başarısız oldu: {hata}", "using_browser_profile": "{Tarayıcı} profi<PERSON> k<PERSON>: {user_data_dir}", "tempmail_plus_verification_completed": "Tempmailplus doğrulaması başarıyla tamamlandı", "tempmail_plus_email_missing": "Tempmailplus e -posta yapılandırılmadı", "tempmail_plus_init_failed": "Tempmailplus'u başlatılamadı: {hata}", "tempmail_plus_config_missing": "Tempmailplus yapılandırması eksik", "tempmail_plus_initialized": "Tempmailplus başarıyla başlatıldı", "tempmail_plus_disabled": "Tempmailplus devre dışı bırakıldı", "no_new_processes_detected": "İzlemek için yeni {tarayıcı} işlemi tespit edilmedi", "make_sure_browser_is_properly_installed": "{Tarayıcı} 'nın düzgün yüklü olduğundan emin olun"}, "auth": {"title": "<PERSON><PERSON><PERSON>", "checking_auth": "Kimlik Dosyası Kontrol Ediliyor", "auth_not_found": "Kimlik Dosyası Bulunamadı", "auth_file_error": "<PERSON><PERSON> Dosyası Hatası: {error}", "reading_auth": "Kimlik Dosyası Okunuyor", "updating_auth": "Kimlik Bilgileri Güncelleniyor", "auth_updated": "Kimlik Bilgileri Başarıyla Güncellendi", "auth_update_failed": "Kimlik Bilgileri Güncellemesi Başarısız: {error}", "auth_file_created": "Kimlik Dosyası Oluşturuldu", "auth_file_create_failed": "Kimlik Dosyası Oluşturma Başarısız: {error}", "press_enter": "Çıkmak için Enter'a Basın", "reset_machine_id": "<PERSON><PERSON><PERSON>", "database_connection_closed": "Veritabanı Bağlantısı Kapatıldı", "database_updated_successfully": "Veritabanı Başarıyla Güncellendi", "connected_to_database": "Veritabanına Bağlanıldı", "updating_pair": "Anahtar-<PERSON><PERSON><PERSON> Çifti Güncelleniyor", "db_not_found": "Veritabanı dosyası bulunamadı: {path}", "db_permission_error": "Veritabanı dosyasına erişilemiyor. Lütfen izinleri kontrol edin", "db_connection_error": "Veritabanına bağlantı başarısız: {error}"}, "control": {"generate_email": "Yeni E-posta Oluşturuluyor", "blocked_domain": "Engellenmiş Alan Ad<PERSON>", "select_domain": "<PERSON><PERSON><PERSON><PERSON> Alan Adı Seçiliyor", "copy_email": "E-posta Adresi <PERSON>", "enter_mailbox": "<PERSON><PERSON> Ku<PERSON> G<PERSON>", "refresh_mailbox": "<PERSON><PERSON>", "check_verification": "Doğrulama Kodu Kontrol <PERSON>", "verification_found": "Doğrulama Kodu Bulundu", "verification_not_found": "Doğrulama Kodu Bulunamadı", "browser_error": "Tarayıcı Kontrol <PERSON>: {error}", "navigation_error": "Gezinme Hatası: {error}", "email_copy_error": "E-posta Kopyalama Hatası: {error}", "mailbox_error": "Posta Kutusu Hat<PERSON>ı: {error}", "token_saved_to_file": "Jeton cursor_tokens.txt dosyasına kaydedildi", "navigate_to": "{url} adresine gidiliyor", "generate_email_success": "E-posta Oluşturma Başarılı", "select_email_domain": "E-posta <PERSON>", "select_email_domain_success": "E-posta Alan Adı Seçimi Başarılı", "get_email_name": "E-posta Adı Al", "get_email_name_success": "E-posta Adı Alma Başarılı", "get_email_address": "E-posta Adresi Al", "get_email_address_success": "E-posta Adresi Alma Başarılı", "enter_mailbox_success": "Posta Kutusuna Giriş Başarılı", "found_verification_code": "Doğrulama Kodu Bulundu", "get_cursor_session_token": "Cursor Oturum Jetonu Al", "get_cursor_session_token_success": "Cursor Oturum Jetonu Alma Başarılı", "get_cursor_session_token_failed": "Cursor Oturum Jetonu Alma Başarısız", "save_token_failed": "Jeton Kaydetme Başarısız", "database_updated_successfully": "Veritabanı Başarıyla Güncellendi", "database_connection_closed": "Veritabanı Bağlantısı Kapatıldı", "no_valid_verification_code": "Geçerli Doğrulama Kodu Yok"}, "email": {"starting_browser": "Tarayıcı Başlatılıyor", "visiting_site": "E-posta alan ad<PERSON>ı ziyaret ediliyor", "create_success": "E-posta Başarıyla Oluşturuldu", "create_failed": "E-posta Oluşturma Başarısız", "create_error": "E-posta Oluşturma Hatası: {error}", "refreshing": "E-posta Yenileniyor", "refresh_success": "E-posta Başarıyla Yenilendi", "refresh_error": "E-posta <PERSON>: {error}", "refresh_button_not_found": "Yenileme Düğmesi Bulunamadı", "verification_found": "Doğrulama Bulundu", "verification_not_found": "Doğrulama Bulunamadı", "verification_error": "Doğrulama Hatası: {error}", "verification_code_found": "Doğrulama Kodu Bulundu", "verification_code_not_found": "Doğrulama Kodu Bulunamadı", "verification_code_error": "Doğrulama Kodu Hatası: {error}", "address": "E-posta Adresi", "all_domains_blocked": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>", "no_available_domains_after_filtering": "Filtrelemeden Sonra Kullanılabilir Alan <PERSON>", "switching_service": "{service} Servisine Geçiliyor", "domains_list_error": "<PERSON> Listesi Alınamadı: {error}", "failed_to_get_available_domains": "Kullanılabil<PERSON>ı Alınamadı", "domains_excluded": "<PERSON><PERSON>: {domains}", "failed_to_create_account": "Hesap Oluşturma Başarısız", "account_creation_error": "<PERSON>sap <PERSON>luşturma Hatası: {error}", "blocked_domains": "<PERSON><PERSON><PERSON><PERSON>: {domains}", "blocked_domains_loaded": "<PERSON><PERSON><PERSON><PERSON> Alan <PERSON>: {count}", "blocked_domains_loaded_error": "<PERSON>gel<PERSON><PERSON> Alan <PERSON>ı Yükleme Hatası: {error}", "blocked_domains_loaded_success": "Engellenen Alan Adları Başarıyla Yüklendi", "blocked_domains_loaded_timeout": "Engellenen Alan <PERSON> Yükleme Zaman Aşımı: {timeout}s", "blocked_domains_loaded_timeout_error": "Engellenen Alan <PERSON> Yükleme Zaman Aşımı Hatası: {error}", "available_domains_loaded": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: {count}", "domains_filtered": "<PERSON><PERSON><PERSON><PERSON><PERSON>: {count}", "trying_to_create_email": "E-posta oluşturulmaya çalışılıyor: {email}", "domain_blocked": "<PERSON>: {domain}", "no_display_found": "Ekran bulunamadı. X sunucusunun çalıştığından emin olun.", "try_export_display": "Deneyin: <PERSON><PERSON><PERSON>a aktarma ekranı =: 0", "try_install_chromium": "Deneyin: sudo apt <PERSON>rom ta<PERSON>ıcısını yü<PERSON>in", "extension_load_error": "Uzatma yükü hatası: {hata}", "make_sure_chrome_chromium_is_properly_installed": "Krom/kromun düzgün takıldığından emin olun", "using_chrome_profile": "Chrome Profilini <PERSON>: {USER_DATA_DIR}"}, "update": {"title": "Cursor Otomatik Güncellemeyi Devre Dışı Bırak", "disable_success": "Otomatik Güncelleme Başarıyla Devre Dışı Bırakıldı", "disable_failed": "Otomatik Güncellemeyi Devre Dışı Bırakma Başarısız: {error}", "press_enter": "Çıkmak için Enter'a Basın", "start_disable": "Otomatik Güncellemeyi Devre Dışı Bırakma Başlatılıyor", "killing_processes": "İşlemler Sonlandırılıyor", "processes_killed": "İşlemler Sonlandırıldı", "removing_directory": "<PERSON><PERSON> Kaldırılıyor", "directory_removed": "<PERSON><PERSON>ldırıldı", "creating_block_file": "Engelleme Dosyası Oluşturuluyor", "block_file_created": "Engelleme Dosyası Oluşturuldu", "clearing_update_yml": "Update.yml dosyasını temizleme", "update_yml_cleared": "update.yml dosyası temizlendi", "unsupported_os": "Desteklenmemiş işletim sistemi: {System}", "block_file_already_locked": "Blok dosyası zaten kilitlendi", "yml_already_locked_error": "update.yml dosyası zaten kilitli hata: {hata}", "update_yml_not_found": "update.yml dosyası bulunamadı", "yml_locked_error": "update.yml dosyası kilitli hata: {error}", "remove_directory_failed": "<PERSON><PERSON> kaldırılamadı: {hata}", "create_block_file_failed": "Blok dosyası oluşturulamadı: {error}", "yml_already_locked": "update.yml dosyası zaten kilitli", "block_file_locked_error": "Blok Dosya <PERSON>: {hata}", "directory_locked": "<PERSON><PERSON> kilitli: {yol}", "block_file_already_locked_error": "Blok dosyası zaten kilitli hata: {hata}", "clear_update_yml_failed": "Update.yml dosyasını temizleyemedi: {error}", "yml_locked": "update.yml dosyası kilitlendi", "block_file_locked": "Blok dosyası kilitlendi"}, "updater": {"checking": "Güncellemeler kontrol ediliyor...", "new_version_available": "<PERSON><PERSON> sürüm mevcut! (Mevcut: {current}, En son: {latest})", "updating": "En son s<PERSON><PERSON><PERSON><PERSON>lleniyor. Program otomatik olarak yeniden başlatılacak.", "up_to_date": "En son s<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>anıyorsunuz.", "check_failed": "G<PERSON><PERSON>llemeler kontrol edilemedi: {error}", "continue_anyway": "Mevcut sürü<PERSON>le devam ediliyor...", "update_confirm": "En son sü<PERSON><PERSON><PERSON> gü<PERSON>k istiyor musunuz? (Y/n)", "update_skipped": "<PERSON><PERSON><PERSON><PERSON><PERSON>.", "invalid_choice": "Geçersiz seçim. Lütfen 'Y' veya 'n' girin.", "development_version": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {current} > {latest}", "changelog_title": "Değişiklik günlüğü", "rate_limit_exceeded": "GitHub API oranı sınırı aştı. Güncelleme kontrolünü atlama."}, "totally_reset": {"title": "Cursor'ı Tamamen Sıfırla", "checking_config": "Yapılandırma Dosyası Kontrol Ediliyor", "config_not_found": "Yapılandırma Dosyası Bulunamadı", "no_permission": "Yapılandırma Dosyası Okunamıyor veya Yazılamıyor, Lütfen Dosya İzinlerini Kontrol Edin", "reading_config": "Mevcut <PERSON>", "creating_backup": "Yapılandırma Yedeği Oluşturuluyor", "backup_exists": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> Atlanıyor", "generating_new_machine_id": "<PERSON><PERSON> Ma<PERSON>e <PERSON>ği Oluşturuluyor", "saving_new_config": "Yeni <PERSON>landırma JSON'a Kaydediliyor", "success": "Cursor Başarıyla Sıfırlandı", "error": "Cursor Sıfırlama Başarısız: {error}", "press_enter": "Çıkmak için Enter'a Basın", "reset_machine_id": "<PERSON><PERSON><PERSON>", "database_connection_closed": "Veritabanı Bağlantısı Kapatıldı", "database_updated_successfully": "Veritabanı Başarıyla Güncellendi", "connected_to_database": "Veritabanına Bağlanıldı", "updating_pair": "Anahtar-<PERSON><PERSON><PERSON> Çifti Güncelleniyor", "db_not_found": "Veritabanı dosyası bulunamadı: {path}", "db_permission_error": "Veritabanı dosyasına erişilemiyor. Lütfen izinleri kontrol edin", "db_connection_error": "Veritabanına bağlantı başarısız: {error}", "feature_title": "ÖZELLİKLER", "feature_1": "Cursor AI ayarları ve yapılandırmalarının tamamen kaldırılması", "feature_2": "AI geçmişi ve komutları dahil tüm önbelleğe alınmış verileri temizler", "feature_3": "Deneme süresini aşma tespitini atlatmak için makine kimliğini sıfırlar", "feature_4": "Rastgele yeni makine tanımlayıcıları oluşturur", "feature_5": "Özel uzantıları ve tercihleri kaldırır", "feature_6": "Deneme süresi bilgilerini ve aktivasyon verilerini sıfırlar", "feature_7": "Gizli lisans ve deneme süresiyle ilgili dosyalar için derin tarama", "feature_8": "Cursor o<PERSON>yan <PERSON>yaları ve uygulamaları güvenle korur", "feature_9": "Windows, macOS ve Linux ile uyumludur", "disclaimer_title": "YASAL UYARI", "disclaimer_1": "<PERSON><PERSON> <PERSON>, tüm Cursor AI ayarlarını,", "disclaimer_2": "yapılandırmalarını ve önbelleğe alınmış verileri kalıcı olarak silecektir. Bu işlem geri alınamaz.", "disclaimer_3": "Kod dosyalarınız ETKİLENMEYECEK ve bu araç", "disclaimer_4": "yalnızca Cursor AI editör dosyalarını ve deneme süresi algılama mekanizmalarını hedef almak için tasarlanmıştır.", "disclaimer_5": "Sisteminizde bulunan diğer uygulamalar etkilenmeyecektir.", "disclaimer_6": "Bu aracı çalıştırdıktan sonra Cursor AI'yi yeniden kurmanız gerekecektir.", "disclaimer_7": "Kullanım sorumluluğu size aittir", "confirm_title": "<PERSON><PERSON> etmek istediğinizden emin misiniz?", "confirm_1": "<PERSON><PERSON> <PERSON><PERSON>, tüm Cursor AI ayarlarını,", "confirm_2": "yapılandırmalarını ve önbelleğe alınmış verileri silecektir. Bu işlem geri alınamaz.", "confirm_3": "Kod dosyalarınız ETKİLENMEYECEK ve bu araç", "confirm_4": "yalnızca Cursor AI editör dosyalarını ve deneme süresi algılama mekanizmalarını hedef almak için tasarlanmıştır.", "confirm_5": "Sisteminizde bulunan diğer uygulamalar etkilenmeyecektir.", "confirm_6": "Bu aracı çalıştırdıktan sonra Cursor AI'yi yeniden kurmanız gerekecektir.", "confirm_7": "Kullanım sorumluluğu size aittir", "invalid_choice": "Lütfen 'Y' veya 'n' girin", "skipped_for_safety": "Güvenlik için atland<PERSON> (Cursor ile ilgili değil): {path}", "deleted": "<PERSON><PERSON><PERSON>: {path}", "error_deleting": "{path} silinirken hata: {error}", "not_found": "<PERSON><PERSON><PERSON> b<PERSON>: {path}", "resetting_machine_id": "Deneme süresi algılamasını atlatmak için makine tanımlayıcıları sıfırlanıyor...", "created_machine_id": "<PERSON><PERSON> makine kimliği oluşturuldu: {path}", "error_creating_machine_id": "<PERSON><PERSON><PERSON> k<PERSON>ği dosyası oluşturulurken hata {path}: {error}", "error_searching": "{path} içindeki dosyalar aranırken hata: {error}", "created_extended_trial_info": "<PERSON>ni genişletilmiş deneme bilgisi oluşturuldu: {path}", "error_creating_trial_info": "Deneme bilgisi dosyası oluşturulurken hata {path}: {error}", "resetting_cursor_ai_editor": "Cursor AI Editor sıfırlanıyor... Lütfen bekleyin.", "reset_cancelled": "Sıfırlama iptal edildi. Herhangi bir değişiklik yapmadan çıkılıyor.", "windows_machine_id_modification_skipped": "Windows makine kimliği değişikliği atlandı: {error}", "linux_machine_id_modification_skipped": "Linux machine-id değişikliği atlandı: {error}", "note_complete_machine_id_reset_may_require_running_as_administrator": "Not: Tam makine kim<PERSON>i sıfırlaması yönetici olarak çalıştırmayı gerektirebilir", "note_complete_system_machine_id_reset_may_require_sudo_privileges": "Not: Tam sistem machine-id sıfırlaması sudo ayrıcalıkları gerektirebilir", "windows_registry_instructions": "📝 NOT: Windows'ta tam sıfırlama için kayıt defteri girdilerini de temizlemeniz gerekebilir.", "windows_registry_instructions_2": "   'regedit' çalıştırın ve HKEY_CURRENT_USER\\Software\\ altında 'Cursor' veya 'CursorAI' içeren anahtarları arayıp silin.\n", "reset_log_1": "Cursor AI tamamen sıfırlandı ve deneme süresi algılaması atlatıldı!", "reset_log_2": "Değişikliklerin etkili olması için lütfen sisteminizi yeniden başlatın.", "reset_log_3": "Cursor AI'yi yeniden kurmanız gerekecek ve şimdi yeni bir deneme süreniz olmalı.", "reset_log_4": "En iyi sonuçlar i<PERSON><PERSON> da düşü<PERSON>ün:", "reset_log_5": "Yeni bir deneme süresi için kaydolurken farklı bir e-posta adresi kullanın", "reset_log_6": "Mümkünse, IP adresinizi değiştirmek için VPN kullanın", "reset_log_7": "Cursor AI'nin web sitesini ziyaret etmeden önce tarayıcı çerezlerinizi ve önbelleği temizleyin", "reset_log_8": "<PERSON><PERSON><PERSON> de<PERSON><PERSON>, Cursor AI'yi farklı bir konuma kurmayı deneyin", "reset_log_9": "<PERSON><PERSON><PERSON> bir sorun<PERSON>, <PERSON><PERSON><PERSON> gidin ve https://github.com/yeongpin/cursor-free-vip/issues adresinde bir sorun o<PERSON>un", "unexpected_error": "Beklenmeyen bir hata o<PERSON>: {error}", "report_issue": "Lütfen bu sorunu https://github.com/yeongpin/cursor-free-vip/issues adresindeki Github So<PERSON> bildirin", "keyboard_interrupt": "İşlem kullanıcı tarafından kesildi. Çıkılıyor...", "return_to_main_menu": "Ana menüye dönülüyor...", "process_interrupted": "İşlem kesildi. Çıkılıyor...", "press_enter_to_return_to_main_menu": "Ana menüye dönmek için Enter'a basın...", "removing_known": "Bilinen deneme/lisans dosyaları kaldırılıyor", "performing_deep_scan": "Ek deneme/lisans dosyaları için derin tarama yapılıyor", "found_additional_potential_license_trial_files": "{count} ek potansiyel lisans/deneme dosyası bulundu", "checking_for_electron_localstorage_files": "Electron localStorage dosyaları kontrol ediliyor", "no_additional_license_trial_files_found_in_deep_scan": "<PERSON>in ta<PERSON>da ek lisans/deneme dosyası bulunamadı", "removing_electron_localstorage_files": "Electron localStorage dosyaları kaldırılıyor", "electron_localstorage_files_removed": "Electron localStorage dosyaları kaldırıldı", "electron_localstorage_files_removal_error": "Electron localStorage dosyaları kaldırılırken hata: {error}", "removing_electron_localstorage_files_completed": "Electron localStorage dosyaları kaldırma işlemi tamamlandı", "warning_title": "UYARI", "direct_advanced_navigation": "Gelişmiş sekmede doğrudan gezinmeyi denemek", "delete_input_error": "<PERSON><PERSON><PERSON> silme hatası: {hata}", "delete_input_not_found_continuing": "<PERSON><PERSON>, yine de devam etmeye çalışıyor", "advanced_tab_not_found": "Gelişmiş sekme birden fazla denemeden sonra bulunamadı", "advanced_tab_error": "Gelişmiş sekme bulma hatası: {hata}", "delete_input_not_found": "<PERSON><PERSON> fazla denemeden sonra bulunmayan onay giri<PERSON>ini silme", "failed_to_delete_file": "Dosyayı silemedi: {yol}", "operation_cancelled": "İşlem iptal edildi. Herhangi bir değişiklik yapmadan çıkmak.", "removed": "Kaldırıldı: {yol}", "warning_6": "Bu aracı çalıştırdıktan sonra İmleç AI'sını tekrar ayarlamanız gerekecektir.", "delete_input_retry": "<PERSON><PERSON><PERSON>, {deneme}/{max_attempts} dene", "warning_4": "Yalnızca imleci AI düzenleyici dosyaları ve deneme algılama mekanizmalarını hedeflemek.", "cursor_reset_failed": "İmleç AI Editör Sıfırlama Başarısız: {Hata}", "login_redirect_failed": "<PERSON><PERSON><PERSON> yönlendirme başarısız oldu, <PERSON><PERSON><PERSON><PERSON> gezinmeyi deniyor ...", "warning_5": "Sisteminizdeki diğer uygulamalar etkilenmeyecektir.", "failed_to_delete_file_or_directory": "Dosyayı veya dizinini silmemedi: {Path}", "failed_to_delete_directory": "<PERSON><PERSON> silinmemesi: {yol}", "resetting_cursor": "İmleç AI düzenleyicisini sıfırlama ... Lütfen bekleyin.", "cursor_reset_completed": "İmleç AI editörü tamamen sıfırlandı ve deneme algılama atlandı!", "warning_3": "Kod dosyalarınız etkilenmeyecek ve araç tasarlandı", "advanced_tab_retry": "G<PERSON>şmiş sekme bulunamadı, {deneme}/{max_attempts} deneme", "completed_in": "{Zaman} saniyelerde tama<PERSON>landı", "advanced_tab_clicked": "Gelişmiş sekmesine tıklandı", "already_on_settings": "Zaten Ayarlar Sayfasında", "delete_button_retry": "<PERSON>l <PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>, {deneme}/{max_attempts} dene", "found_danger_zone": "Bulunan Tehlike Bölgesi Bölümü", "failed_to_remove": "Kaldırılamadı: {yol}", "failed_to_reset_machine_guid": "<PERSON><PERSON><PERSON> kı<PERSON>unu sıfırl<PERSON>", "deep_scanning": "Ek deneme/lisans dosyaları için derin tarama yapmak", "delete_button_clicked": "Hesabı Sil düğmesine tıklandı", "warning_7": "<PERSON><PERSON>ğunuzda kullanın", "delete_button_not_found": "Birden çok denemeden sonra bulunamadı Hesap düğ<PERSON>ini <PERSON> düğ<PERSON>", "delete_button_error": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> bulma hatası: {hata}", "warning_2": "yapılandırmalar ve önbelleğe alınmış veriler. Bu eylem geri alınamaz.", "warning_1": "<PERSON><PERSON> eylem tüm imleci AI ayarlarını siler,", "navigating_to_settings": "<PERSON><PERSON>lar sayfasına gitmek ...", "cursor_reset_cancelled": "İmleç AI Editör Sıfırlandı İptal edildi. Herhangi bir değişiklik yapmadan çıkmak."}, "chrome_profile": {"title": "Chrome Profil <PERSON>", "select_profile": "Kullanılacak Chrome profilini seçin:", "profile_list": "Mevcut profiller:", "default_profile": "Varsayılan Profil", "profile": "Profil {number}", "no_profiles": "Chrome profili bulunamadı", "error_loading": "Chrome profilleri yü<PERSON> hata: {error}", "profile_selected": "Seçilen profil: {profile}", "invalid_selection": "Geçersiz seçim. Lütfen tekrar deneyin", "warning_chrome_close": "Uyarı: Bu işlem tüm çalışan Chrome işlemlerini kapatacaktır"}, "restore": {"title": "<PERSON><PERSON><PERSON> G<PERSON>", "starting": "<PERSON><PERSON><PERSON> kim<PERSON>ği geri yükleme işlemi başlatılıyor", "no_backups_found": "<PERSON><PERSON> b<PERSON>", "available_backups": "<PERSON><PERSON><PERSON>", "select_backup": "<PERSON><PERSON> yü<PERSON><PERSON>k için bir ye<PERSON> seçin", "to_cancel": "iptal etmek için", "operation_cancelled": "İşlem iptal edildi", "invalid_selection": "Geçersiz seçim", "please_enter_number": "Lütfen geçerli bir numara girin", "missing_id": "<PERSON><PERSON><PERSON> kimlik: {id}", "read_backup_failed": "<PERSON><PERSON> okunamadı: {error}", "current_file_not_found": "Mevcut depolama dosyası bulunamadı", "current_backup_created": "Mevcut depolama <PERSON>ı<PERSON>ın ye<PERSON>ğ<PERSON> oluşturuldu", "storage_updated": "Depolama <PERSON>yası başarıyla güncellendi", "update_failed": "<PERSON><PERSON><PERSON> dosyası güncellenemedi: {error}", "sqlite_not_found": "SQLite veritabanı bulunamadı", "updating_sqlite": "SQLite veritabanı güncelleniyor", "updating_pair": "Ana<PERSON>r<PERSON><PERSON><PERSON><PERSON> güncelleniyor", "sqlite_updated": "SQLite veritabanı başarıyla güncellendi", "sqlite_update_failed": "SQLite veritabanı güncellenemedi: {error}", "machine_id_backup_created": "MachineId dosyasının ye<PERSON>ği oluşturuldu", "backup_creation_failed": "<PERSON><PERSON>: {error}", "machine_id_updated": "MachineId dosyası başarıyla güncellendi", "machine_id_update_failed": "MachineId dosyası güncellenemedi: {error}", "updating_system_ids": "Sistem kimlikleri güncelleniyor", "system_ids_update_failed": "Sistem kimlikleri güncellenemedi: {error}", "permission_denied": "İzin reddedildi. Yönetici olarak çalıştırmayı deneyin", "windows_machine_guid_updated": "Windows makine GUID'i başarıyla güncellendi", "update_windows_machine_guid_failed": "Windows makine GUID'i güncellenemedi: {error}", "windows_machine_id_updated": "Windows makine kimliği başarıyla güncellendi", "update_windows_machine_id_failed": "Windows makine kimliği güncellenemedi: {error}", "sqm_client_key_not_found": "SQMClient kayıt anahtarı bulunamadı", "update_windows_system_ids_failed": "Windows sistem kimlikleri güncellenemedi: {error}", "macos_platform_uuid_updated": "macOS platform UUID'si başarıyla güncellendi", "failed_to_execute_plutil_command": "plutil komutu <PERSON>", "update_macos_system_ids_failed": "macOS sistem kimlikleri güncellenemedi: {error}", "ids_to_restore": "<PERSON><PERSON> makine kimlikleri", "confirm": "Bu kimlikleri geri yüklemek istediğinizden emin misiniz?", "success": "<PERSON><PERSON><PERSON> k<PERSON> başarı<PERSON> geri <PERSON>", "process_error": "<PERSON><PERSON> işlemi hatası: {error}", "press_enter": "<PERSON><PERSON> etmek için Enter tuşuna basın"}, "oauth": {"no_chrome_profiles_found": "Varsayılan kullanarak krom profil bulunamadı", "failed_to_delete_account": "<PERSON><PERSON>b<PERSON> silemedi: {hata}", "starting_new_authentication_process": "Yeni Kimlik Doğrulama İşlemine Başlamak ...", "found_email": "E -posta bulundu: {e -posta}", "github_start": "<PERSON><PERSON><PERSON>", "already_on_settings_page": "Zaten Ayarlar sayfasında!", "starting_github_authentication": "Başlangıç ​​Github Kimlik Doğrulaması ...", "account_is_still_valid": "<PERSON><PERSON><PERSON> hala geç<PERSON> (kullanım: {kullanım})", "status_check_error": "Durum kontrol hatası: {hata}", "authentication_timeout": "Kimlik Doğrulama Zaman Aşımı", "using_first_available_chrome_profile": "İlk kullanılabilir krom profilini kullanma: {profil}", "no_compatible_browser_found": "Uyumlu tarayıcı bulunamadı. Lütfen Google Chrome veya Chromium'u yükleyin.", "usage_count": "Kullanım Sayısı: {<PERSON>llan<PERSON>m}", "google_start": "Google Start", "authentication_successful_getting_account_info": "Kimlik doğrulama başarılı, hesap bilgileri almak ...", "found_chrome_at": "Chrome'da bulundu: {yol}", "error_getting_user_data_directory": "Hata kullanıcı veri dizinini alır: {hata}", "error_finding_chrome_profile": "<PERSON><PERSON> profilini bulma hatası, varsayılan: {hata}", "auth_update_success": "Yetkilendirme Başarısı Güncelle", "authentication_successful": "Kimlik Doğrulama Başarılı - E -posta: {E -posta}", "authentication_failed": "Kimlik doğrulama başarısız oldu: {hata}", "warning_browser_close": "Uyarı: <PERSON><PERSON>, tü<PERSON> {tarayıcı} işlemlerini kapatacaktır", "supported_browsers": "{Platform} i<PERSON><PERSON>", "authentication_button_not_found": "Kimlik Doğrulama düğmesi bulunamadı", "starting_new_google_authentication": "Yeni Google Kimlik Doğrulamasına Başlamak ...", "waiting_for_authentication": "Kimlik doğrulamasını bekliyorum ...", "found_default_chrome_profile": "Varsayılan Chrome Profili Bulundu", "could_not_check_usage_count": "Ku<PERSON>ım sayısını kontrol edemedi: {hata}", "starting_browser": "Tarayıcı Başlangıç: {Path}", "token_extraction_error": "Jeton Çıkarma Hatası: {Hata}", "profile_selection_error": "Profil se<PERSON><PERSON> s<PERSON>nda hata: {hata}", "warning_could_not_kill_existing_browser_processes": "Uyarı: Mevcut tarayıcı işlemlerini öldüremedi: {hata}", "browser_failed_to_start": "Tara<PERSON>ıcı başlayamadı: {hata}", "redirecting_to_authenticator_cursor_sh": "Authenticator.cursor.sh'a yönlendirme ...", "starting_re_authentication_process": "Yeniden kimlik doğrulama sürecine başlama ...", "found_browser_data_directory": "Bulunan Tarayıcı Veri Dizini: {Path}", "browser_not_found_trying_chrome": "<PERSON><PERSON>un yerine Chrome'u denemek {tarayıcı} bulamadım", "found_cookies": "<PERSON><PERSON>nd<PERSON> {Count} <PERSON><PERSON><PERSON><PERSON>", "auth_update_failed": "Auth güncellemesi başarısız oldu", "browser_failed_to_start_fallback": "Tara<PERSON>ıcı başlayamadı: {hata}", "failed_to_delete_expired_account": "Süresi dolmuş hesabı silemedi", "navigating_to_authentication_page": "Kimlik doğrulama sayfasına gitmek ...", "initializing_browser_setup": "Tarayıcı kurulumunu başlat<PERSON> ...", "browser_closed": "Tarayıcı kapalı", "failed_to_delete_account_or_re_authenticate": "Hesabı silmemedi veya yeniden kimlik doğrulanmadı: {hata}", "detected_platform": "Tespit edilen platform: {platform}", "failed_to_extract_auth_info": "Yetkilendirme Bilgisi Çıkarılamadı: {Hata}", "starting_google_authentication": "Google Kimlik Doğrulamasını Başlat ...", "browser_failed": "Tara<PERSON>ıcı başlayamadı: {hata}", "using_browser_profile": "Tarayıcı Profilini <PERSON>: {Profil}", "consider_running_without_sudo": "Senaryoyu sudo olmadan <PERSON>ıştırmayı düşünün", "try_running_without_sudo_admin": "Sudo/yönetici ayrıcalıkları olmadan çalışmayı deneyin", "page_changed_checking_auth": "<PERSON><PERSON>, kimlik doğrulama ...", "running_as_root_warning": "Tarayıcı Otomasyonu için Kök Olarak Koşu Önermez", "please_select_your_google_account_to_continue": "Lütfen devam etmek için Google hesabınızı seçin ...", "browser_setup_failed": "Tarayıcı kurulumu başarısız oldu: {hata}", "missing_authentication_data": "<PERSON><PERSON><PERSON> Doğrulama Verileri: {Data}", "using_configured_browser_path": "Yapılandırılmış {tarayıcı} yolu kullanma: {yol}", "killing_browser_processes": "{Tarayıcı} süreçlerini öldürmek ...", "could_not_find_usage_count": "<PERSON><PERSON><PERSON>m sayımı bulamadım: {hata}", "browser_setup_completed": "Tarayıcı kurulumu başarıyla tamamlandı", "account_has_reached_maximum_usage": "<PERSON><PERSON><PERSON> maks<PERSON> kull<PERSON> ula<PERSON>t<PERSON>, {silme}", "could_not_find_email": "E -posta bulamadım: {hata}", "found_browser_user_data_dir": "Bulundu {tarayıcı} Kullanıcı Veri Dizini: {Path}", "user_data_dir_not_found": "{Browser} Kullanıcı Veri Dizini {Path} adresinde bulunamadı, bunun yerine Chrome'u den<PERSON>k", "invalid_authentication_type": "Geçersiz kimlik doğrulama türü"}, "account_delete": {"delete_input_not_found": "<PERSON><PERSON> fazla denemeden sonra bulunmayan onay giri<PERSON>ini silme", "logging_in": "Google ile oturum açma ...", "confirm_button_not_found": "Birden fazla denemeden sonra bulunamadı düğmesini onay<PERSON>ın", "confirm_button_error": "<PERSON><PERSON> Bul<PERSON>: {hata}", "delete_button_clicked": "Hesabı Sil düğmesine tıklandı", "confirm_prompt": "<PERSON><PERSON> etmek istediğinden emin misin? (E/H):", "cancelled": "<PERSON><PERSON><PERSON> silme iptal edildi.", "delete_button_error": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> bulma hatası: {hata}", "interrupted": "<PERSON><PERSON>p silme işlemi kullanıcı tarafından kesintiye uğradı.", "error": "<PERSON><PERSON><PERSON> silme sı<PERSON>ında hata: {hata}", "delete_input_not_found_continuing": "<PERSON><PERSON>, yine de devam etmeye çalışıyor", "advanced_tab_retry": "G<PERSON>şmiş sekme bulunamadı, {deneme}/{max_attempts} deneme", "waiting_for_auth": "Google kimlik doğrulamasını bekliyorum ...", "typed_delete": "<PERSON>ay kutusunda yazılan \"Sil\"", "trying_settings": "Ayarlar sayfasında gezinmeye çalışıyorum ...", "delete_input_retry": "<PERSON><PERSON><PERSON>, {deneme}/{max_attempts} dene", "email_not_found": "E -posta bulunamadı: {hata}", "delete_button_not_found": "Birden çok denemeden sonra bulunamadı Hesap düğ<PERSON>ini <PERSON> düğ<PERSON>", "already_on_settings": "Zaten Ayarlar Sayfasında", "failed": "<PERSON><PERSON><PERSON> silme i<PERSON><PERSON>i başarısız oldu veya iptal edildi.", "warning": "Uyarı: <PERSON><PERSON>, <PERSON><PERSON><PERSON> hesabınızı kalıcı olarak silecektir. Bu eylem geri alınamaz.", "direct_advanced_navigation": "Gelişmiş sekmede doğrudan gezinmeyi denemek", "advanced_tab_not_found": "Gelişmiş sekme birden fazla denemeden sonra bulunamadı", "auth_timeout": "<PERSON><PERSON> doğrulama zaman aşımı, yine de devam ediyor ...", "select_google_account": "Lütfen Google hesabınızı seçin ...", "google_button_not_found": "Google Giriş Düğmesi bulunamadı", "found_danger_zone": "Bulunan Tehlike Bölgesi Bölümü", "account_deleted": "<PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON> si<PERSON>!", "starting_process": "<PERSON><PERSON><PERSON> silme <PERSON><PERSON> b<PERSON> ...", "advanced_tab_error": "Gelişmiş sekme bulma hatası: {hata}", "delete_button_retry": "<PERSON>l <PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>, {deneme}/{max_attempts} dene", "login_redirect_failed": "<PERSON><PERSON><PERSON> yönlendirme başarısız oldu, <PERSON><PERSON><PERSON><PERSON> gezinmeyi deniyor ...", "unexpected_error": "Beklenmedik hata: {hata}", "delete_input_error": "<PERSON><PERSON><PERSON> silme hatası: {hata}", "login_successful": "<PERSON><PERSON><PERSON> başarılı", "advanced_tab_clicked": "Gelişmiş sekmesine tıklandı", "unexpected_page": "G<PERSON>şten sonra beklenmedik sayfa: {url}", "found_email": "E -posta bulundu: {e -posta}", "title": "İmleç Google Hesap Silme Aracı", "navigating_to_settings": "<PERSON><PERSON>lar sayfasına gitmek ...", "success": "İmleç hesabın<PERSON>z başarıyla silindi!", "confirm_button_retry": "<PERSON><PERSON><PERSON>ad<PERSON>, Deneme {deneme}/{max_attempts}"}, "auth_check": {"token_length": "Jeton uzunluğu: {uzunluk} karakterler", "usage_response_status": "<PERSON><PERSON><PERSON>m Yanıt Durumu: {yanıt}", "operation_cancelled": "Kullanıcı tarafından iptal edildi işlem", "error_getting_token_from_db": "Veritabanından jeton alma hatası: {hata}", "checking_usage_information": "Kullanım bilgilerini kontrol etmek ...", "usage_response": "Kullanım yanıtı: {yanıt}", "authorization_failed": "Yetkilendirme başarısız oldu!", "authorization_successful": "Yetkilendirme başarılı!", "request_timeout": "İstek zaman aşımına uğramış", "check_error": "Hata Kontrolü Yetkilendirme: {hata}", "connection_error": "Bağlantı hatası", "invalid_token": "Geçersiz jeton", "enter_token": "İmleç jetonunuzu girin:", "check_usage_response": "Kullanım yanıtını kontrol edin: {yanıt}", "user_unauthorized": "Kullanıcı yetkisiz", "token_found_in_db": "Veritabanında bulundu jeton", "checking_authorization": "Yetkilendirme kontrolü ...", "error_generating_checksum": "Hata Oluşturma Noktası: {hata}", "unexpected_error": "Beklenmedik hata: {hata}", "token_source": "Veritabanından jeton veya manuel olarak girdi mi? (D/M, Varsayılan: D)", "user_authorized": "Kullanıcı yetkili", "token_not_found_in_db": "Token veritabanında bulunamadı", "jwt_token_warning": "Token JWT formatında gibi görünüyor, ancak API Check beklenmedik bir durum kodu döndürdü. Jeton geçerli olabilir, ancak API erişimi kısıtlanmıştır.", "unexpected_status_code": "Beklenmedik durum kodu: {code}", "getting_token_from_db": "Veritabanından jeton almak ...", "cursor_acc_info_not_found": "Cursor_acc_info.py bulunamadı"}, "manual_auth": {"auth_type_selected": "Seçilen Kimlik Doğrulama Türü: {Type}", "proceed_prompt": "İlerlemek? (E/H):", "auth_type_github": "<PERSON><PERSON><PERSON><PERSON>", "confirm_prompt": "Lütfen aşağıdaki bilgileri onaylayın:", "invalid_token": "Geçersiz jeton. Kimlik doğrulama iptal edildi.", "continue_anyway": "Yine de devam et? (E/H):", "token_verified": "Token başarıyla doğrulandı!", "error": "Hata: {hata}", "auth_update_failed": "Kimlik doğrulama bilgilerini güncelleyemedi", "auth_type_auth0": "Auth_0 (varsay<PERSON><PERSON>)", "auth_type_prompt": "Kimlik Doğrulama Türünü seçin:", "verifying_token": "Token geçerliliğini doğrulamak ...", "auth_updated_successfully": "Kimlik doğrulama bilgileri başarıyla güncellendi!", "email_prompt": "E -posta girin (rastgele e -posta i<PERSON><PERSON> bo<PERSON> bırakın):", "token_prompt": "İmleç jetonunuzu girin (Access_token/Refresh_token):", "title": "Manuel imleç kimlik doğrulaması", "token_verification_skipped": "Token doğrulaması atlandı (check_user_authorized.py bulunamadı)", "random_email_generated": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rastgele e -posta: {e -posta}", "token_required": "<PERSON><PERSON> g<PERSON>", "auth_type_google": "Google", "operation_cancelled": "Operasyon iptal edildi", "token_verification_error": "Hata Doğrulama Jetonu: {Hata}", "updating_database": "İmleç Kimlik Doğrulama Veritabanını Güncelleme ..."}, "token": {"refreshing": "Serinlet<PERSON> jeton ...", "extraction_error": "Hata Çıkarma jetonu: {hata}", "invalid_response": "Yenileme Sunucusundan Geçersiz JSON Yanıtı", "no_access_token": "<PERSON><PERSON><PERSON> o<PERSON>ak eri<PERSON>im beli<PERSON> yok", "connection_error": "<PERSON><PERSON><PERSON>u yenilemek için bağlantı hatası", "unexpected_error": "Jeton Yenileme <PERSON> Beklenmedik Hata: {Hata}", "server_error": "<PERSON><PERSON>u Hatası Yenile: HTTP {Durum}", "refresh_success": "Token başarıyla yenilendi! {Days} günleri için geçerlidir (süresi dolar: {süresi sona erer})", "request_timeout": "<PERSON><PERSON><PERSON><PERSON> ye<PERSON>leme isteği zaman aşımına uğramış", "refresh_failed": "Token Yenileme Başarısız: {Hata}"}, "browser_profile": {"profile_selected": "Seçilen profil: {profil}", "default_profile": "Varsayılan profil", "no_profiles": "Yok {tarayıcı} profilleri bulunamadı", "select_profile": "Kullanılmak üzere {tarayıcı} profilini seçin:", "error_loading": "Hata Yükleme {tarayıc<PERSON>} Profiller: {hata}", "invalid_selection": "Geçersiz seçim. Lütfen tekrar deneyin.", "title": "Tarayıcı Profil Seçimi", "profile": "Profil {numara}", "profile_list": "Kullanılabilir {tarayıcı} profiller:"}, "github_register": {"feature2": "Rastgele kimlik bilgileriyle yeni bir GitHub hesabı kaydeder.", "feature6": "<PERSON><PERSON><PERSON> kimlik bilgilerini bir dosyaya ka<PERSON>.", "starting_automation": "Başlangıç ​​<PERSON>yonu ...", "feature1": "1secmail kullanarak geçici bir e -posta oluşturur.", "title": "GitHub + İmleç AI Kayıt Otomasyonu", "github_username": "<PERSON><PERSON><PERSON> k<PERSON> adı", "check_browser_windows_for_manual_intervention_or_try_again_later": "<PERSON> mü<PERSON> i<PERSON><PERSON> ta<PERSON> pencerelerini kontrol edin veya daha sonra tekrar deneyin.", "warning1": "Bu komut dosyası, GitHub/Cursor Hizmet Şartlarını ihlal edebilecek hesap oluşturmayı otomatikleştirir.", "feature4": "GitHub Kimlik Doğrulaması kullanarak imleç AI'da günlükler.", "invalid_choice": "Geçersiz seçim. Lütfen 'Eve<PERSON>' veya 'hayır' girin", "completed_successfully": "GitHub + im<PERSON><PERSON> kaydı başarıyla tamamlandı!", "warning2": "İnternet erişimi ve idari ayrıcalıklar gerektirir.", "registration_encountered_issues": "GitHub + im<PERSON><PERSON> kaydı sorunlarla karşılaştı.", "credentials_saved": "Bu kimlik bilgileri github_cursor_accounts.txt adresine kaydedildi", "feature3": "GitHub e -postasını otomatik olarak doğrular.", "github_password": "<PERSON><PERSON><PERSON>", "features_header": "<PERSON><PERSON><PERSON><PERSON>", "feature5": "<PERSON><PERSON>e algılamasını atlamak için makine kimliğini sıfırlar.", "warning4": "So<PERSON>lu ve kendi sorumluluğunuzda kullanın.", "warning3": "Captcha veya ek doğrulama otomasyonu kesintiye uğratabilir.", "cancelled": "Operasyon iptal edildi", "warnings_header": "Uyarı", "program_terminated": "Kullanıcı tarafından feshedilen program", "confirm": "<PERSON><PERSON> etmek istediğinden emin misin?", "email_address": "E -posta adresi"}, "account_info": {"subscription": "Abonelik", "failed_to_get_account_info": "Hesap bilgi<PERSON>i alamadı", "subscription_type": "Abonelik türü", "pro": "Profesyonel", "failed_to_get_account": "Hesap bilgi<PERSON>i alamadı", "config_not_found": "Yapılandırma bulunamadı.", "premium_usage": "Premium kullanım", "failed_to_get_subscription": "Abonelik bilgileri alamadı", "basic_usage": "<PERSON><PERSON>", "premium": "Prim", "free": "Özgür", "email_not_found": "E -posta bulunamadı", "title": "<PERSON><PERSON><PERSON>", "inactive": "<PERSON><PERSON><PERSON>", "remaining_trial": "<PERSON><PERSON>", "enterprise": "<PERSON><PERSON><PERSON><PERSON>", "failed_to_get_usage": "Kullanım bilgisi alamadı", "lifetime_access_enabled": "<PERSON><PERSON><PERSON>", "usage_not_found": "Kullanım bulunamadı", "days_remaining": "<PERSON><PERSON>", "failed_to_get_token": "Jeton alamadı", "token": "<PERSON><PERSON>", "subscription_not_found": "Abonelik bilgileri bulunamadı", "days": "<PERSON><PERSON><PERSON><PERSON>", "team": "Takım", "token_not_found": "Jeton bulunamadı", "active": "Aktif", "email": "E -posta", "pro_trial": "Profes<PERSON><PERSON>", "failed_to_get_email": "E -posta adresi al<PERSON>ı", "trial_remaining": "<PERSON><PERSON> profesyonel deneme", "usage": "Kullanım"}, "config": {"config_updated": "Yapılandırma <PERSON>", "configuration": "Konfigürasyon", "file_owner": "<PERSON><PERSON><PERSON> sahibi: {sahibi}", "error_checking_linux_paths": "Linux yollarını kontrol etme hatası: {hata}", "storage_file_is_empty": "Depolama dosyası boş: {storage_path}", "config_directory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "documents_path_not_found": "Mevcut dizini kull<PERSON> bulu<PERSON><PERSON>yan belgeler yolu", "config_not_available": "Yapılandırma mevcut değil", "please_make_sure_cursor_is_installed_and_has_been_run_at_least_once": "Lütfen imlecin kurulduğundan ve en az bir kez çalıştırıldığından emin olun", "neither_cursor_nor_cursor_directory_found": "{Config_base}", "config_created": "Config <PERSON><PERSON><PERSON>: {config_file}", "using_temp_dir": "Hata nedeniyle geçici dizin kullanma: {yol} (hata: {error})", "storage_file_not_found": "Depolama dosyası bulunamadı: {storage_path}", "the_file_might_be_corrupted_please_reinstall_cursor": "<PERSON><PERSON><PERSON> b<PERSON>, lüt<PERSON> imleci yeniden yükleyin", "error_getting_file_stats": "<PERSON><PERSON>a İstatistikleri Alma Hatası: {hata}", "enabled": "Etkinleştirilmiş", "backup_created": "<PERSON><PERSON><PERSON><PERSON>: {Path}", "file_permissions": "<PERSON><PERSON><PERSON>: {<PERSON><PERSON><PERSON>}", "config_setup_error": "<PERSON><PERSON> Ayarlama Yapılandırma: {hata}", "config_force_update_enabled": "Ya<PERSON><PERSON><PERSON><PERSON>rma <PERSON>, <PERSON><PERSON><PERSON> Güncellemeyi Gerçekleştirerek", "config_removed": "Yapılandırma dosyası zorla güncelleme için kaldırıldı", "file_size": "<PERSON><PERSON><PERSON>: {boyut} bayt", "error_reading_storage_file": "Hata Okurken Depolama Dosyası: {hata}", "config_force_update_disabled": "Yapılandırma Dosya Kuvveti Güncellemesi Devre Dışı, Zorunlu Güncellemeyi Atlama", "config_dir_created": "Config dizin oluşturuldu: {yol}", "config_option_added": "Ya<PERSON><PERSON>landırma seçeneği eklendi: {option}", "file_group": "Do<PERSON>a Grubu: {Group}", "and": "Ve", "backup_failed": "Yapılandırılamadı Yapılandırma: {hata}", "force_update_failed": "<PERSON><PERSON><PERSON> Güncelleme Yapılandırması Başarısız: {Hata}", "storage_directory_not_found": "Depol<PERSON> Dizini b<PERSON>ad<PERSON>: {depolama_dir}", "also_checked": "Ayrıca kontrol edildi {yol}", "try_running": "Koşmayı deneyin: {command}", "storage_file_found": "Depolama dosyası bulundu: {storage_path}", "disabled": "<PERSON><PERSON><PERSON>", "storage_file_is_valid_and_contains_data": "Depolama dosyası geçerlidir ve veri içerir", "permission_denied": "İzin Reddedildi: {Storage_Path}"}, "bypass": {"found_product_json": "Bulunan ürün.json: {yol}", "starting": "İmleç Sürümü Bypass'ı Başlat ...", "version_updated": "{Eski} 'dan {new}' e gü<PERSON><PERSON><PERSON> sürüm", "menu_option": "Bypass imleç sürüm kontrolü", "unsupported_os": "Desteklenmemiş işletim sistemi: {System}", "backup_created": "<PERSON><PERSON><PERSON><PERSON>: {Path}", "current_version": "<PERSON><PERSON><PERSON><PERSON><PERSON> sürüm: {sürüm}", "no_write_permission": "<PERSON><PERSON><PERSON> i<PERSON> yazma izni yok: {yol}", "localappdata_not_found": "LocalAppdata Çevre Değişkeni bulunamadı", "write_failed": "Ürün ya<PERSON>ı<PERSON>adı.json: {hata}", "description": "<PERSON><PERSON> a<PERSON>ç, s<PERSON><PERSON><PERSON><PERSON> kısıtlamalarını atlamak için imlecin ürününü değiştirir.", "bypass_failed": "Sürüm bypass başarısız oldu: {hata}", "title": "İmleç Versiyonu Bypass Aracı", "no_update_needed": "Güncellemeye gerek yok. Geçerli sürüm {sürüm} zaten> = 0.46.0", "read_failed": "<PERSON><PERSON><PERSON><PERSON>.json: {hata}", "stack_trace": "Stack Trace", "product_json_not_found": "ürün.json ortak linux yollarında bulunamadı", "file_not_found": "<PERSON><PERSON><PERSON> bulunamad<PERSON>: {yol}"}, "bypass_token_limit": {"description": "<PERSON><PERSON> araç, jeton sınırını atlamak için workbench.desktop.main.js dosyasını değiştirir", "press_enter": "<PERSON><PERSON> etmek için Enter tuşuna basın ...", "title": "Baypas Token Limit Aracı"}, "tempmail": {"general_error": "Bir hata olu<PERSON>: {hata}", "no_email": "İmleç doğrulama e -postası bulunamadı", "config_error": "<PERSON><PERSON><PERSON><PERSON><PERSON>rma dosya hatası: {error}", "extract_code_failed": "Çıkarma Doğrulama Kodu Başarısız: {Hata}", "configured_email": "Yapılandırılmış e -posta: {e -posta}", "checking_email": "İmleç doğrulama e -postasını kontrol etmek ...", "check_email_failed": "E -postanın başar<PERSON>s<PERSON>z olduğunu kontrol edin: {hata}", "no_code": "Doğrulama kodu alamadı", "email_found": "İmleç doğrulama e -postası bulundu", "verification_code": "Doğrulama kodu: {kod}"}}