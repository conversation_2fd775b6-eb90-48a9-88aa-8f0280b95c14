{"menu": {"title": "Options Disponibles", "exit": "<PERSON><PERSON><PERSON> le <PERSON>", "reset": "Réinitialiser l'ID Machine", "register": "Enregistrer un Nouveau Compte Cursor", "register_google": "S'inscrire avec un Compte Google", "register_github": "S'inscrire avec un Compte GitHub", "register_manual": "Enregistrer Cursor avec un E-mail Personnalisé", "quit": "Fermer l'Application Cursor", "select_language": "Change<PERSON> <PERSON>", "input_choice": "Veuillez entrer votre choix ({choices})", "invalid_choice": "Sélection invalide. Veuillez entrer un numéro de {choices}", "program_terminated": "Programme terminé par l'utilisateur", "error_occurred": "Une erreur s'est produite : {error}. Veuillez réessayer", "press_enter": "Appuyez sur Entrée pour quitter", "disable_auto_update": "Désactiver la Mise à Jour Automatique de Cursor", "lifetime_access_enabled": "ACCÈS À VIE ACTIVÉ", "totally_reset": "Réinitialisation Complète de Cursor", "outdate": "Obsolete", "temp_github_register": "Inscription GitHub temporaire", "coming_soon": "Bientôt", "fixed_soon": "Bientôt Corrigé", "contribute": "Contribuer au Projet", "config": "Afficher la Configuration", "delete_google_account": "Supp<PERSON>er le Compte Google Cursor", "continue_prompt": "Continuer ? (y/N) : ", "operation_cancelled_by_user": "Opération annulée par l'utilisateur", "exiting": "Fermeture ……", "bypass_version_check": "Ignorer la Vérification de Version de Cursor", "check_user_authorized": "Vérifier l'Autorisation de l'Utilisateur", "bypass_token_limit": "Contourner la limite de tokens", "restore_machine_id": "Restaurer l'ID de machine depuis une sauvegarde", "select_chrome_profile": "Sélectionnez Chrome Profil", "admin_required": "Exécution en tant qu'exécutable, les privilèges de l'administrateur requis.", "language_config_saved": "Configuration du langage enregistré avec succès", "lang_invalid_choice": "Choix non valide. Veuillez saisir l'une des options suivantes: ({Lang_Choices})", "manual_custom_auth": "Auth person<PERSON><PERSON><PERSON> manuel", "admin_required_continue": "Poursuivant sans privilèges d'administrateur."}, "languages": {"ar": "<PERSON><PERSON>", "en": "<PERSON><PERSON><PERSON>", "zh_cn": "<PERSON><PERSON> simplifié", "zh_tw": "Chinois traditionnel", "vi": "<PERSON><PERSON>", "nl": "Néerlandais", "de": "Allemand", "fr": "Français", "pt": "Portugais", "ru": "<PERSON><PERSON>", "es": "Espagnol", "tr": "<PERSON><PERSON>", "bg": "Bulgare", "it": "italien", "ja": "japonais"}, "quit_cursor": {"start": "Début de la Fermeture de Cursor", "no_process": "Aucun Processus Cursor en Cours", "terminating": "<PERSON><PERSON><PERSON><PERSON> {pid}", "waiting": "En Attente de la Fin du Processus", "success": "Tous les Processus Cursor sont Fermés", "timeout": "<PERSON><PERSON><PERSON> d'Attente du Processus : {pids}", "error": "Erreur Survenue : {error}"}, "reset": {"title": "Outil de Réinitialisation de l'ID Machine de Cursor", "checking": "Vérification du Fichier de Configuration", "not_found": "Fichier de Configuration Non Trouvé", "no_permission": "Impossible de Lire ou d'<PERSON><PERSON><PERSON><PERSON> le Fichier de Configuration, Veuillez Vérifier les Permissions", "reading": "Lecture de la Configuration Actuelle", "creating_backup": "Création d'une Sauvegarde de la Configuration", "backup_exists": "Fichier de Sauvegarde Existe <PERSON>, Étape de Sauvegarde Ignorée", "generating": "Génération d'un Nouvel ID Machine", "saving_json": "Sauvegarde de la Nouvelle Configuration en JSON", "success": "ID Machine Réinitialisé avec Succès", "new_id": "Nouvel ID Machine", "permission_error": "Erreur de Permission : {error}", "run_as_admin": "Veuillez Essayer d'Exécuter ce Programme en tant qu'Administrateur", "process_error": "<PERSON><PERSON>ur de Processus de Réinitialisation : {error}", "updating_sqlite": "Mise à Jour de la Base de Données SQLite", "updating_pair": "Mise à Jour de la Paire Clé-Valeur", "sqlite_success": "Base de Données SQLite Mise à Jour avec Succès", "sqlite_error": "Échec de la Mise à Jour de la Base de Données SQLite : {error}", "press_enter": "Appuyez sur Entrée pour Continuer", "unsupported_os": "Système d'Exploitation Non Pris en Charge : {os}", "linux_path_not_found": "Chemin Linux Non Trouvé", "updating_system_ids": "Mise à Jour des IDs Système", "system_ids_updated": "IDs Système Mis à Jour avec Succès", "system_ids_update_failed": "Échec de la Mise à Jour des IDs Système : {error}", "windows_guid_updated": "GUID Windows Mis à Jour avec Succès", "windows_permission_denied": "Permission Windows Refusée", "windows_guid_update_failed": "Échec de la Mise à Jour du GUID Windows", "macos_uuid_updated": "UUID macOS Mis à Jour avec Succès", "plutil_command_failed": "Commande plutil <PERSON>", "start_patching": "Démarrage du Patching de getMachineId", "macos_uuid_update_failed": "Échec de la Mise à Jour de l'UUID macOS", "current_version": "Version Actuelle de Cursor : {version}", "patch_completed": "Patching de getMachineId Terminé", "patch_failed": "Échec du Patching de getMachineId : {error}", "version_check_passed": "Vérification de la Version de Cursor Réussie", "file_modified": "<PERSON><PERSON><PERSON>", "version_less_than_0_45": "Version de Cursor < 0.45.0, Patching de getMachineId Ignoré", "detecting_version": "Détection de la Version de Cursor", "patching_getmachineid": "Patching de getMachineId", "version_greater_than_0_45": "Version de Cursor >= 0.45.0, Patching de getMachineId", "permission_denied": "Permission Refusée : {error}", "backup_created": "<PERSON><PERSON><PERSON><PERSON>", "update_success": "<PERSON>se à Jour Réussie", "update_failed": "Échec de la Mise à Jour : {error}", "windows_machine_guid_updated": "GUID de la Machine Windows Mis à Jour avec Succès", "reading_package_json": "Lecture du package.json {path}", "invalid_json_object": "Objet JSON Invalide", "no_version_field": "Aucun Champ de Version Trouvé dans le package.json", "version_field_empty": "Champ de Version Vide", "invalid_version_format": "Format de Version Invalide : {version}", "found_version": "Version Trouvée : {version}", "version_parse_error": "<PERSON><PERSON><PERSON> d'Ana<PERSON><PERSON> de la Version : {error}", "package_not_found": "Package.json Non Trouvé : {path}", "check_version_failed": "Échec de la Vérification de la Version : {error}", "stack_trace": "<PERSON>le", "version_too_low": "Version de Cursor Trop Basse : {version} < 0.45.0", "no_write_permission": "Pas de Permission d'Écriture : {path}", "path_not_found": "Chemin Non Trouvé : {path}", "modify_file_failed": "Échec de la Modification du Fichier : {error}", "windows_machine_id_updated": "ID de la Machine Windows Mis à Jour avec Succès", "update_windows_machine_id_failed": "Échec de la Mise à Jour de l'ID de la Machine Windows : {error}", "update_windows_machine_guid_failed": "Échec de la Mise à Jour du GUID de la Machine Windows : {error}", "file_not_found": "Fichier Non Trouvé : {path}"}, "register": {"title": "Outil d'Enregistrement de Cursor", "start": "Démarrage du Processus d'Enregistrement...", "handling_turnstile": "Traitement de la Vérification de Sécurité...", "retry_verification": "Nouvelle Tentative de Vérification...", "detect_turnstile": "Vérification de la Sécurité...", "verification_success": "Vérification de Sécurité Réussie", "starting_browser": "Ouverture du Navigateur...", "form_success": "Formulaire Soumis avec Succès", "browser_started": "Navigateur Ouvert avec Succès", "waiting_for_second_verification": "En Attente de la Vérification par E-mail...", "waiting_for_verification_code": "En Attente du Code de Vérification...", "password_success": "Mot de Passe Défini avec Succès", "password_error": "Impossible de Définir le Mot de Passe : {error}. Veuillez Réessayer", "waiting_for_page_load": "Chargement de la Page...", "first_verification_passed": "Première Vérification Réussie", "mailbox": "Boîte de Réception Accédée avec Succès", "register_start": "Démarrer l'Enregistrement", "form_submitted": "Formulaire Soumis, Démarrage de la Vérification...", "filling_form": "Remplissage du Formulaire", "visiting_url": "Visite de l'URL", "basic_info": "Informations de Base Soumises", "handle_turnstile": "Traitement du Tourniquet", "no_turnstile": "<PERSON><PERSON><PERSON>", "turnstile_passed": "Tourni<PERSON>", "verification_start": "Démarrage de l'Obtention du Code de Vérification", "verification_timeout": "Délai d'Attente du Code de Vérification", "verification_not_found": "Aucun Code de Vérification Trouvé", "try_get_code": "Essayer | {attempt} d'Obtenir le Code de Vérification | Temps Restant : {time}s", "get_account": "Obtention des Informations du Compte", "get_token": "Obtention du Jeton de Session Cursor", "token_success": "Jeton Obtenu avec Succès", "token_attempt": "Essayer | {attempt} fois d'Obt<PERSON><PERSON> le Jeton | Nouvelle Tentative dans {time}s", "token_max_attempts": "Nombre Maximum de Tentatives Atteint ({max}) | Échec de l'Obtention du Jeton", "token_failed": "Échec de l'Obtention du Jeton : {error}", "account_error": "Échec de l'Obtention des Informations du Compte : {error}", "press_enter": "Appuyez sur Entrée pour Continuer", "browser_start": "Démarrage du Navigateur", "open_mailbox": "Ouverture de la Page de la Boîte de Réception", "email_error": "Échec de l'Obtention de l'Adresse E-mail", "setup_error": "Erreur de Configuration de l'E-mail : {error}", "start_getting_verification_code": "Démarrage de l'Obtention du Code de Vérification, Nouvelle Tentative dans 60s", "get_verification_code_timeout": "D<PERSON>lai d'Attente de l'Obtention du Code de Vérification", "get_verification_code_success": "Code de Vérification Obtenu avec Succès", "try_get_verification_code": "Essayer | {attempt} d'Obtenir le Code de Vérification | Temps Restant : {remaining_time}s", "verification_code_filled": "Code de Vérification Rempli", "login_success_and_jump_to_settings_page": "Connexion Réussie et Accès à la Page des Paramètres", "detect_login_page": "Détection de la Page de Connexion, Démarrage de la Connexion...", "cursor_registration_completed": "Enregistrement de Cursor Terminé!", "set_password": "Définir le Mot de Passe", "basic_info_submitted": "Informations de Base Soumises", "cursor_auth_info_updated": "Informations d'Authentification de Cursor Mises à Jour", "cursor_auth_info_update_failed": "Échec de la Mise à Jour des Informations d'Authentification de Cursor", "reset_machine_id": "Réinitialiser l'ID Machine", "account_info_saved": "Informations du Compte Enregistrées", "save_account_info_failed": "Échec de l'Enregistrement des Informations du Compte", "get_email_address": "Obtenir l'Adresse E-mail", "update_cursor_auth_info": "Mettre à Jour les Informations d'Authentification de Cursor", "register_process_error": "<PERSON><PERSON>ur du Processus d'Enregistrement : {error}", "setting_password": "Définir le Mot de Passe", "manual_code_input": "<PERSON><PERSON>", "manual_email_input": "<PERSON><PERSON> de l'E-mail", "password": "<PERSON><PERSON>", "first_name": "Prénom", "last_name": "Nom de Famille", "exit_signal": "Signal de Sortie", "email_address": "<PERSON>resse E-mail", "config_created": "Configuration Créée", "verification_failed": "Échec de la Vérification", "verification_error": "Erreur de Vérification : {error}", "config_option_added": "Option de Configuration Ajoutée : {option}", "config_updated": "Configuration Mise à Jour", "password_submitted": "<PERSON>t de Passe Soumis", "total_usage": "Utilisation Totale : {usage}", "setting_on_password": "Définir le Mot de Passe", "getting_code": "Obtention du Code de Vérification, Nouvelle Tentative dans 60s", "using_browser": "Utilisation de {navigateur} navigateur: {path}", "could_not_track_processes": "Impossible de suivre {<PERSON><PERSON><PERSON>} Processus: {<PERSON><PERSON><PERSON>}", "try_install_browser": "Essayez d'installer le navigateur avec votre gestionnaire de packages", "tempmail_plus_verification_started": "Démarrage du processus de vérification TempmailPlus", "max_retries_reached": "Tentatives de réessayer maximales atteintes. L'inscription a échoué.", "tempmail_plus_enabled": "TempmailPlus est activé", "browser_path_invalid": "{Browser} Le chemin n'est pas valide, en utilisant le chemin par défaut", "human_verify_error": "Impossible de vérifier que l'utilisateur est humain. Réessayer ...", "using_tempmail_plus": "Utilisation de TempmailPlus pour la vérification des e-mails", "tracking_processes": "Processus de suivi {count} {<PERSON><PERSON><PERSON>}", "tempmail_plus_epin_missing": "Tempmailplus epin n'est pas configuré", "tempmail_plus_verification_failed": "La vérification tempmailplus a échoué: {error}", "using_browser_profile": "Utilisation du profil {<PERSON><PERSON><PERSON>} de: {user_data_dir}", "tempmail_plus_verification_completed": "La vérification TempmailPlus terminée avec succès", "tempmail_plus_email_missing": "Le courrier électronique TempmailPlus n'est pas configuré", "tempmail_plus_config_missing": "La configuration de tempmailplus est manquante", "tempmail_plus_init_failed": "Échec de l'initialisation de tempmailplus: {error}", "tempmail_plus_initialized": "TempmailPlus a initialisé avec succès", "tempmail_plus_disabled": "TempmailPlus est désactivé", "no_new_processes_detected": "Pas de nouveaux processus {navigateur} détectés pour suivre", "make_sure_browser_is_properly_installed": "Assurez-vous que {<PERSON><PERSON><PERSON>} est correctement installé"}, "auth": {"title": "Gestionnaire d'Authentification de Cursor", "checking_auth": "Vérification du Fichier d'Authentification", "auth_not_found": "Fichier d'Authentification Non Trouvé", "auth_file_error": "<PERSON><PERSON><PERSON> <PERSON> Fichier d'Authentification : {error}", "reading_auth": "Lecture du Fichier d'Authentification", "updating_auth": "Mise à Jour des Informations d'Authentification", "auth_updated": "Informations d'Authentification Mises à Jour avec Succès", "auth_update_failed": "Échec de la Mise à Jour des Informations d'Authentification : {error}", "auth_file_created": "Fichier d'Authentification Créé", "auth_file_create_failed": "Échec de la Création du Fichier d'Authentification : {error}", "press_enter": "Appuyez sur Entrée pour Continuer", "reset_machine_id": "Réinitialiser l'ID Machine", "database_connection_closed": "Connexion à la Base de Données Fermée", "database_updated_successfully": "Base de Données Mise à Jour avec Succès", "connected_to_database": "Connecté à la Base de Données", "updating_pair": "Mise à Jour de la Paire Clé-Valeur", "db_not_found": "Fichier de Base de Données Non Trouvé à : {path}", "db_permission_error": "Impossible d'Accéder au Fichier de Base de Données. Veuillez Vérifier les Permissions", "db_connection_error": "Échec de la Connexion à la Base de Données : {error}"}, "control": {"generate_email": "Générer un Nouvel E-mail", "blocked_domain": "Domaine <PERSON>", "select_domain": "Sélectionner un Domaine Aléatoire", "copy_email": "Copier l'Adresse E-mail", "enter_mailbox": "Entrer dans la Boîte de Réception", "refresh_mailbox": "Actualiser la Boîte de Réception", "check_verification": "Vérifier le Code de Vérification", "verification_found": "Code de Vérification Trouvé", "verification_not_found": "Aucun Code de Vérification Trouvé", "browser_error": "<PERSON><PERSON>ur de Contrôle du Navigateur : {error}", "navigation_error": "Erreur de Navigation : {error}", "email_copy_error": "<PERSON><PERSON><PERSON> de Copie de l'E-mail : {error}", "mailbox_error": "<PERSON><PERSON><PERSON> <PERSON> la Boîte de Réception : {error}", "token_saved_to_file": "Jeton Enregistré dans cursor_tokens.txt", "navigate_to": "Naviguer vers {url}", "generate_email_success": "E-mail Généré avec Succès", "select_email_domain": "Sélectionner le Domaine de l'E-mail", "select_email_domain_success": "Domaine de l'E-mail Sélectionné avec Succès", "get_email_name": "O<PERSON><PERSON>r le Nom de l'E-mail", "get_email_name_success": "Nom de l'E-mail Obtenu avec Succès", "get_email_address": "Obtenir l'Adresse E-mail", "get_email_address_success": "Adresse E-mail Obtenue avec Succès", "enter_mailbox_success": "Entrée dans la Boîte de Réception Réussie", "found_verification_code": "Code de Vérification Trouvé", "get_cursor_session_token": "<PERSON><PERSON><PERSON><PERSON> le Jeton de Session Cursor", "get_cursor_session_token_success": "Jeton de Session Cursor Obtenu avec Succès", "get_cursor_session_token_failed": "Échec de l'Obtention du Jeton de Session Cursor", "save_token_failed": "Échec de l'Enregistrement du Jeton", "database_updated_successfully": "Base de Données Mise à Jour avec Succès", "database_connection_closed": "Connexion à la Base de Données Fermée", "no_valid_verification_code": "Aucun Code de Vérification Valide"}, "email": {"starting_browser": "Démarrage du Navigateur", "visiting_site": "Visite de mail domains", "create_success": "E-mail Créé avec Succès", "create_failed": "Échec de la Création de l'E-mail", "create_error": "Erreur de Création de l'E-mail : {error}", "refreshing": "Actualisation de l'E-mail", "refresh_success": "E-mail Actualisé avec Succès", "refresh_error": "Erreur d'Actualisation de l'E-mail : {error}", "refresh_button_not_found": "Bouton d'Actualisation Non Trouvé", "verification_found": "Vérification Trouvée", "verification_not_found": "Vérification Non Trouvée", "verification_error": "Erreur de Vérification : {error}", "verification_code_found": "Code de Vérification Trouvé", "verification_code_not_found": "Code de Vérification Non Trouvé", "verification_code_error": "Erreur de Code de Vérification : {error}", "address": "<PERSON>resse E-mail", "all_domains_blocked": "Tous les Domaines Sont Bloqués, Changement de Service", "no_available_domains_after_filtering": "Aucun Domaine Disponible Après Filtrage", "switching_service": "Changement vers le Service {service}", "domains_list_error": "Échec de l'Obtention de la Liste des Domaines : {error}", "failed_to_get_available_domains": "Échec de l'Obtention des Domaines Disponibles", "domains_excluded": "Domaines Exclus : {domains}", "failed_to_create_account": "Échec de la Création du Compte", "account_creation_error": "<PERSON><PERSON><PERSON> de Création du Compte : {error}", "domain_blocked": "Domaine Bloqué : {domain}", "no_display_found": "Aucun écran trouvé. Assurez-vous que X Server s'exécute.", "try_export_display": "Essayez: Affichage d'exportation =: 0", "try_install_chromium": "Essayez: sudo apt install chromium-browser", "blocked_domains": "Domaines bloqués: {domaines}", "blocked_domains_loaded_timeout_error": "Domaines bloqués Erreur de délai d'expiration: {Erreur}", "blocked_domains_loaded_success": "Des domaines bloqués chargés avec succès", "extension_load_error": "Erreur de chargement d'extension: {erreur}", "available_domains_loaded": "Domaines disponibles chargés: {count}", "blocked_domains_loaded_error": "Domaines bloqués Erreur chargée: {<PERSON><PERSON>ur}", "make_sure_chrome_chromium_is_properly_installed": "Assurez-vous que Chrome / Chromium est correctement installé", "blocked_domains_loaded_timeout": "Domaines bloqués Timeout chargé: {dé<PERSON> d'expiration}", "domains_filtered": "Domaines filtrés: {count}", "trying_to_create_email": "Essayer de créer un e-mail: {email}", "using_chrome_profile": "Utilisation du profil chrome de: {user_data_dir}", "blocked_domains_loaded": "Domaines bloqués chargés: {count}"}, "update": {"title": "Désactivation de la Mise à Jour Automatique de Cursor", "disable_success": "Mise à Jour Automatique Désactivée avec Succès", "disable_failed": "Échec de la Désactivation de la Mise à Jour Automatique : {error}", "press_enter": "Appuyez sur Entrée pour Continuer", "start_disable": "Démarrage de la Désactivation de la Mise à Jour Automatique", "killing_processes": "<PERSON><PERSON>", "processes_killed": "<PERSON><PERSON>", "removing_directory": "Suppression du Dossier", "directory_removed": "Dossier <PERSON>", "creating_block_file": "Création du Fichier de Blocage", "block_file_created": "<PERSON><PERSON><PERSON>", "clearing_update_yml": "Effacer le fichier update.yml", "update_yml_cleared": "Fichier Update.yml effacé", "unsupported_os": "OS non pris en charge: {System}", "block_file_already_locked": "Le fichier de blocs est déjà verrouillé", "yml_already_locked_error": "Update.yml Fichier <PERSON> déj<PERSON> verro<PERSON>: {erreur}", "update_yml_not_found": "Fichier Update.yml introuvable", "yml_locked_error": "E<PERSON>ur de verrouillage du fichier Update.yml: {erreur}", "remove_directory_failed": "Échec de la suppression du répertoire: {error}", "yml_already_locked": "Le fichier update.yml est déjà verrouillé", "create_block_file_failed": "Échec de la création du fichier de blocs: {error}", "block_file_locked_error": "<PERSON><PERSON><PERSON>ur verrouil<PERSON>e du fi<PERSON>er: {erreur}", "directory_locked": "Le répertoire est verrouillé: {path}", "block_file_already_locked_error": "<PERSON><PERSON><PERSON> le fichier Erreur déjà verrouil<PERSON>e: {erreur}", "clear_update_yml_failed": "Échec de l'effondrement du fichier Update.yml: {error}", "yml_locked": "Le fichier update.yml est verrouillé", "block_file_locked": "Le fichier de blocs est verrouillé"}, "updater": {"checking": "Vérification des mises à jour...", "new_version_available": "Nouvelle version disponible! (Version actuelle: {current}, Version la plus récente: {latest})", "updating": "Mise à jour vers la version la plus récente. Le programme redémarrera automatiquement.", "up_to_date": "Vous utilisez la version la plus récente.", "check_failed": "Échec de la vérification des mises à jour: {error}", "continue_anyway": "Continuer avec la version actuelle...", "update_confirm": "V<PERSON><PERSON><PERSON>-vous mettre à jour vers la version la plus récente? (O/n)", "update_skipped": "Mise à jour ignorée.", "invalid_choice": "Choix invalide. Veuillez entrer 'O' ou 'n'.", "development_version": "Version de Développement {current} > {latest}", "changelog_title": "Journal des modifications", "rate_limit_exceeded": "La limite de taux de l'API GitHub dépasse. Vérification de mise à jour de saut."}, "totally_reset": {"title": "Réinitialiser Complètement Cursor", "checking_config": "Vérification du Fichier de Configuration", "config_not_found": "Fichier de Configuration Non Trouvé", "no_permission": "Impossible de Lire ou d'<PERSON><PERSON><PERSON><PERSON> le Fichier de Configuration, Veuillez Vérifier les Permissions du Fichier", "reading_config": "Lecture de la Configuration Actuelle", "creating_backup": "Création de la Sauvegarde de la Configuration", "backup_exists": "Fichier de Sauvegarde <PERSON>, Passer à la Sauvegarde", "generating_new_machine_id": "Génération d'un Nouvel ID Machine", "saving_new_config": "Enregistrement de la Nouvelle Configuration dans JSON", "success": "Réinitialisation de Cursor Réussie", "error": "Réinitialisation de Cursor Écho<PERSON>e: {error}", "press_enter": "Appuyez sur Entrée pour Continuer", "reset_machine_id": "Réinitialiser l'ID Machine", "database_connection_closed": "Connexion à la Base de Données Fermée", "database_updated_successfully": "Base de Données Mise à Jour avec Succès", "connected_to_database": "Connecté à la Base de Données", "updating_pair": "Updating Key-Value Pair", "db_not_found": "Database file not found at: {path}", "db_permission_error": "Impossible d'Accéder au Fichier de Base de Données. Veuillez Vérifier les Permissions", "db_connection_error": "Échec de la Connexion à la Base de Données : {error}", "feature_title": "Fonctionnalités", "feature_1": "Suppression complète des paramètres et configurations de Cursor AI", "feature_2": "Efface tous les données mises en cache, y compris l'historique et les prompts", "feature_3": "Réinitialise l'ID Machine pour contourner la détection de la période d'essai", "feature_4": "Crée de nouveaux identifiants de machine aléatoires", "feature_5": "Supprime les extensions personnalisées et les préférences", "feature_6": "Réinitialise les informations de la période d'essai et les données d'activation", "feature_7": "Analyse approfondie pour les fichiers cachés liés à la licence et à la période d'essai", "feature_8": "Sauvegarde sécurisée des fichiers non liés à Cursor et applications", "feature_9": "Compatible avec Windows, macOS et Linux", "disclaimer_title": "DISCLAIMER", "disclaimer_1": "Cet outil supprimera définitivement tous les paramètres et configurations de Cursor AI,", "disclaimer_2": "configurations, et les données mises en cache. Cette action ne peut pas être annulée.", "disclaimer_3": "Vos fichiers de code ne seront PAS affectés, et l'outil est conçu", "disclaimer_4": "pour ne cibler que les fichiers de l'éditeur Cursor AI et les mécanismes de détection de la période d'essai.", "disclaimer_5": "Les autres applications sur votre système ne seront PAS affectées.", "disclaimer_6": "Vous de<PERSON><PERSON> régler Cursor AI à nouveau après avoir exécuté cet outil.", "disclaimer_7": "Utilisez à vos risques et périls", "confirm_title": "Êtes-vous sûr de vouloir continuer?", "confirm_1": "Cette action supprimera tous les paramètres et configurations de Cursor AI,", "confirm_2": "configurations, et les données mises en cache. Cette action ne peut pas être annulée.", "confirm_3": "Vos fichiers de code ne seront PAS affectés, et l'outil est conçu", "confirm_4": "pour ne cibler que les fichiers de l'éditeur Cursor AI et les mécanismes de détection de la période d'essai.", "confirm_5": "Les autres applications sur votre système ne seront PAS affectées.", "confirm_6": "Vous de<PERSON><PERSON> régler Cursor AI à nouveau après avoir exécuté cet outil.", "confirm_7": "Utilisez à vos risques et périls", "invalid_choice": "Veuillez entrer 'O' ou 'n'", "skipped_for_safety": "Passé pour la sécurité (non lié à Cursor): {path}", "deleted": "Supprimé: {path}", "error_deleting": "Erreur de suppression de {path}: {error}", "not_found": "Fichier non trouvé: {path}", "resetting_machine_id": "Réinitialisation des identifiants de machine pour contourner la détection de la période d'essai...", "created_machine_id": "Créé un nouvel ID machine: {path}", "error_creating_machine_id": "Erreur de création du fichier ID machine {path}: {error}", "error_searching": "Erreur de recherche dans {path}: {error}", "created_extended_trial_info": "C<PERSON>é un nouveau fichier d'informations de période d'essai étendue: {path}", "error_creating_trial_info": "Erreur de création du fichier d'informations de période d'essai: {path}: {error}", "resetting_cursor_ai_editor": "Réinitialisation de l'éditeur Cursor AI... V<PERSON><PERSON>z patienter.", "reset_cancelled": "Réinitialisation annulée. Exiting sans faire de modifications.", "windows_machine_id_modification_skipped": "Modification de l'ID machine Windows ignorée: {error}", "linux_machine_id_modification_skipped": "Modification de l'ID machine Linux ignorée: {error}", "note_complete_machine_id_reset_may_require_running_as_administrator": "Note: Réinitialisation complète de l'ID machine peut nécessiter d'exécuter en tant qu'administrateur", "note_complete_system_machine_id_reset_may_require_sudo_privileges": "Note: Réinitialisation complète de l'ID machine peut nécessiter des privilèges sudo", "windows_registry_instructions": "📝 NOTE: Pour la réinitialisation complète sur Windows, vous devrez peut-être également nettoyer les entrées du registre.", "windows_registry_instructions_2": "   Exécutez 'regedit' et recherchez les clés contenant 'Cursor' ou 'CursorAI' sous HKEY_CURRENT_USER\\Software\\ et supprimez-les.\n", "reset_log_1": "Cursor AI a été complètement réinitialisé et la détection de la période d'essai a été contournée!", "reset_log_2": "Veuillez redémarrer votre système pour que les modifications prennent effet.", "reset_log_3": "Vous devrez réinstaller Cursor AI et devriez maintenant avoir une période d'essai fraîche.", "reset_log_4": "Pour les meilleurs résultats, considérez également:", "reset_log_5": "Utilisez une autre adresse e-mail lors de l'inscription pour une nouvelle période d'essai", "reset_log_6": "<PERSON> disponible, utilisez un VPN pour changer votre adresse IP", "reset_log_7": "Nettoyez les cookies et le cache de votre navigateur avant de visiter le site web de Cursor AI", "reset_log_8": "Si les problèmes persistent, essayez d'installer Cursor AI dans un autre emplacement", "reset_log_9": "Si vous rencontrez des problèmes, allez au suivi des problèmes Github et créez un problème à https://github.com/yeongpin/cursor-free-vip/issues", "unexpected_error": "Une erreur inattendue est survenue: {error}", "report_issue": "Veuillez signaler ce problème au suivi des problèmes Github à https://github.com/yeongpin/cursor-free-vip/issues", "keyboard_interrupt": "Processus interrompu par l'utilisateur. Exiting...", "return_to_main_menu": "Retour au menu principal...", "process_interrupted": "Processus interrompu. Exiting...", "press_enter_to_return_to_main_menu": "Appuyez sur Entrée pour retourner au menu principal...", "removing_known": "Suppression des fichiers de période d'essai/licence connus", "performing_deep_scan": "Exécution d'une analyse approfondie pour les fichiers de période d'essai/licence supplémentaires", "found_additional_potential_license_trial_files": "Trouvé {count} fichiers de période d'essai/licence supplémentaires potentiels", "checking_for_electron_localstorage_files": "Vérification des fichiers localStorage Electron", "no_additional_license_trial_files_found_in_deep_scan": "Aucun fichier de licence/période d'essai supplémentaire trouvé dans l'analyse approfondie", "removing_electron_localstorage_files": "Suppression des fichiers localStorage Electron", "electron_localstorage_files_removed": "Fichiers localStorage Electron supprimés", "electron_localstorage_files_removal_error": "Erreur de suppression des fichiers localStorage Electron: {error}", "removing_electron_localstorage_files_completed": "Suppression des fichiers localStorage Electron terminée", "warning_title": "AVERTISSEMENT", "delete_input_error": "E<PERSON>ur Rechercher la suppression de l'entrée: {erreur}", "direct_advanced_navigation": "Essayer la navigation directe vers l'onglet avancé", "delete_input_not_found_continuing": "Supprimer l'entrée de confirmation non trouvée, essayant de continuer de toute façon", "advanced_tab_not_found": "Onglet avancé non trouvé après plusieurs tentatives", "advanced_tab_error": "Erreur Recherche d'onglet Avancé: {Erreur}", "delete_input_not_found": "Supprimer l'entrée de confirmation non trouvée après plusieurs tentatives", "failed_to_delete_file": "Échec de la suppression du fichier: {path}", "operation_cancelled": "Opération annulée. Sortant sans apporter de modifications.", "removed": "Supprimé: {path}", "warning_6": "Vous devrez à nouveau rétablir un curseur AI après avoir exécuté cet outil.", "delete_input_retry": "Supprimer l'entrée introuvable, tentative {tentative} / {max_attempts}", "warning_4": "Pour cibler uniquement les fichiers d'éditeur AI de curseur et les mécanismes de détection d'essai.", "cursor_reset_failed": "Cursor AI Editor réinitialisation a échoué: {error}", "login_redirect_failed": "La redirection de connexion a échoué, essayant la navigation directe ...", "warning_5": "D'autres applications sur votre système ne seront pas affectées.", "failed_to_delete_file_or_directory": "Échec de la suppression du fichier ou du répertoire: {path}", "failed_to_delete_directory": "Échec de la suppression du répertoire: {path}", "resetting_cursor": "Réinitialisation du curseur AI Editor ... Veuillez patienter.", "cursor_reset_completed": "Le rédacteur en chef de Cursor AI a été entièrement réinitialisé et la détection d'essai contournée!", "warning_3": "Vos fichiers de code ne seront pas affectés et l'outil est conçu", "advanced_tab_retry": "On<PERSON>t avancé non trouvé, tentative {tentative} / {max_attempts}", "advanced_tab_clicked": "Cliquez sur l'onglet avancé", "completed_in": "Te<PERSON><PERSON><PERSON> en {temps} secondes", "delete_button_retry": "Bouton de suppression introuvable, tentative {tentative} / {max_attempts}", "already_on_settings": "Déjà sur la page des paramètres", "found_danger_zone": "Section de la zone de danger trouvée", "failed_to_remove": "Échec de la suppression: {path}", "failed_to_reset_machine_guid": "Échec de la réinitialisation de la machine Guid", "deep_scanning": "Effectuer une analyse profonde pour des fichiers d'essai / licence supplémentaires", "delete_button_clicked": "Cliquez sur le bouton Supprimer le compte", "warning_7": "Utiliser à vos risques et périls", "delete_button_not_found": "Supprimer le bouton du compte introuvable après plusieurs tentatives", "delete_button_error": "Erreur Recherche du bouton de suppression: {Erreur}", "warning_1": "Cette action supprimera tous les paramètres de Cursor AI,", "warning_2": "Configurations et données mises en cache. Cette action ne peut pas être annulée.", "navigating_to_settings": "Navigation vers la page des paramètres ...", "cursor_reset_cancelled": "Cursor AI Editor réinitialisé annulé. Sortant sans apporter de modifications."}, "chrome_profile": {"title": "Sélection du Profil Chrome", "select_profile": "Sélectionnez un profil Chrome à utiliser :", "profile_list": "Profils disponibles :", "default_profile": "Profil par <PERSON>", "profile": "Profil {number}", "no_profiles": "Aucun profil Chrome trouvé", "error_loading": "Erreur lors du chargement des profils Chrome : {error}", "profile_selected": "Profil sélectionn<PERSON> : {profile}", "invalid_selection": "Sélection invalide. Veuillez réessayer", "warning_chrome_close": "Attention : <PERSON><PERSON> fermera tous les processus Chrome en cours d'exécution"}, "restore": {"title": "Restaurer l'ID de machine depuis une sauvegarde", "starting": "Démarrage du processus de restauration de l'ID de machine", "no_backups_found": "<PERSON><PERSON><PERSON> sauvegarde trouvée", "available_backups": "Sauvegardes disponibles", "select_backup": "Sélectionnez une sauvegarde à restaurer", "to_cancel": "pour annuler", "operation_cancelled": "Opération annulée", "invalid_selection": "Sélection invalide", "please_enter_number": "Veuillez entrer un numéro valide", "missing_id": "ID manquant : {id}", "read_backup_failed": "Échec de lecture du fichier de sauvegarde : {error}", "current_file_not_found": "Fichier de stockage actuel introuvable", "current_backup_created": "Sauvegarde du fichier de stockage actuel créée", "storage_updated": "Fichier de stockage mis à jour avec succès", "update_failed": "Échec de la mise à jour du fichier de stockage : {error}", "sqlite_not_found": "Base de données SQLite introuvable", "updating_sqlite": "Mise à jour de la base de données SQLite", "updating_pair": "Mise à jour de la paire clé-valeur", "sqlite_updated": "Base de données SQLite mise à jour avec succès", "sqlite_update_failed": "Échec de la mise à jour de la base de données SQLite : {error}", "machine_id_backup_created": "Sauvegarde du fichier machineId créée", "backup_creation_failed": "Échec de création de la sauvegarde : {error}", "machine_id_updated": "Fichier machineId mis à jour avec succès", "machine_id_update_failed": "Échec de la mise à jour du fichier machineId : {error}", "updating_system_ids": "Mise à jour des ID système", "system_ids_update_failed": "Échec de la mise à jour des ID système : {error}", "permission_denied": "Permission refusée. Veuillez essayer d'exécuter en tant qu'administrateur", "windows_machine_guid_updated": "GUID de machine Windows mis à jour avec succès", "update_windows_machine_guid_failed": "Échec de la mise à jour du GUID de machine Windows : {error}", "windows_machine_id_updated": "ID de machine Windows mis à jour avec succès", "update_windows_machine_id_failed": "Échec de la mise à jour de l'ID de machine Windows : {error}", "sqm_client_key_not_found": "Clé de registre SQMClient introuvable", "update_windows_system_ids_failed": "Échec de la mise à jour des ID système Windows : {error}", "macos_platform_uuid_updated": "UUID de plateforme macOS mis à jour avec succès", "failed_to_execute_plutil_command": "Échec d'exécution de la commande plutil", "update_macos_system_ids_failed": "Échec de la mise à jour des ID système macOS : {error}", "ids_to_restore": "ID de machine à restaurer", "confirm": "Êtes-vous sûr de vouloir restaurer ces ID ?", "success": "ID de machine restauré avec succès", "process_error": "Erreur du processus de restauration : {error}", "press_enter": "Appuyez sur Entrée pour continuer"}, "oauth": {"no_chrome_profiles_found": "Aucun profil chromé trouvé, en utilisant par défaut", "failed_to_delete_account": "Échec de la suppression du compte: {error}", "starting_new_authentication_process": "<PERSON><PERSON><PERSON>rer un nouveau processus d'authentification ...", "found_email": "Email trouvé: {email}", "github_start": "<PERSON><PERSON><PERSON> Start", "already_on_settings_page": "Déjà sur la page des paramètres!", "starting_github_authentication": "Démarrage de l'authentification GitHub ...", "status_check_error": "Erreur de vérification de l'état: {erreur}", "account_is_still_valid": "Le compte est toujours valide (utilisation: {usage})", "authentication_timeout": "<PERSON><PERSON><PERSON> d'authentification", "usage_count": "Compte d'utilisation: {Utilisation}", "using_first_available_chrome_profile": "Utilisation du premier profil chrome disponible: {profil}", "google_start": "Google Start", "no_compatible_browser_found": "Aucun navigateur compatible trouvé. Veuillez installer Google Chrome ou Chromium.", "authentication_successful_getting_account_info": "Authentification ré<PERSON>ie, obtenir des informations de compte ...", "found_chrome_at": "Trouvé chrome à: {path}", "error_getting_user_data_directory": "Erreur d'obtention du répertoire des données utilisateur: {erreur}", "error_finding_chrome_profile": "Erreur Recherche de profil Chrome, en utilisant la valeur par défaut: {Erreur}", "auth_update_success": "Auth à jour le succès", "authentication_successful": "Authentification réussie - Email: {e-mail}", "authentication_failed": "Échec de l'authentification: {erreur}", "warning_browser_close": "AVERTISSEMENT: cela fermera tous les processus exécutés {navigateur}", "supported_browsers": "Navigateurs pris en charge pour {plate-forme}", "authentication_button_not_found": "Bouton d'authentification introuvable", "starting_new_google_authentication": "Démarrage de la nouvelle authentification Google ...", "waiting_for_authentication": "En attente de l'authentification ...", "found_default_chrome_profile": "Profil chromé par défaut", "starting_browser": "Département du navigateur à: {Path}", "could_not_check_usage_count": "Impossible de vérifier le nombre d'utilisation: {error}", "token_extraction_error": "Erreur d'extraction de jeton: {erreur}", "profile_selection_error": "Erreur pendant la sélection du profil: {erreur}", "warning_could_not_kill_existing_browser_processes": "AVERTISSEMENT: Impossible de tuer les processus de navigateur existants: {Erreur}", "browser_failed_to_start": "Le navigateur n'a pas réussi à démarrer: {error}", "redirecting_to_authenticator_cursor_sh": "Redirection vers Authenticator.cursor.sh ...", "starting_re_authentication_process": "Démarrage du processus de réauthentification ...", "found_browser_data_directory": "Répertoire de données du navigateur trouvé: {path}", "browser_not_found_trying_chrome": "Impossible de trouver {<PERSON><PERSON><PERSON>}, essayant à la place Chrome", "found_cookies": "Cookies trouvés {count}", "auth_update_failed": "La mise à jour de l'authentique a échoué", "browser_failed_to_start_fallback": "Le navigateur n'a pas réussi à démarrer: {error}", "failed_to_delete_expired_account": "Échec de la suppression du compte expiré", "navigating_to_authentication_page": "Navigation vers la page d'authentification ...", "initializing_browser_setup": "Initialisation de la configuration du navigateur ...", "browser_closed": "Navigateur fermé", "failed_to_delete_account_or_re_authenticate": "Échec de la suppression du compte ou de la ré-authentification: {error}", "detected_platform": "Plate-forme détectée: {plate-forme}", "failed_to_extract_auth_info": "Échec de l'extraction d'informations sur l'authentification: {error}", "starting_google_authentication": "Démarrage de l'authentification Google ...", "browser_failed": "Le navigateur n'a pas réussi à démarrer: {error}", "using_browser_profile": "Utilisation du profil du navigateur: {profil}", "consider_running_without_sudo": "Envisagez d'exécuter le script sans sudo", "try_running_without_sudo_admin": "Essayez de fonctionner sans privilèges sudo / administrateur", "running_as_root_warning": "En fonctionnement comme racine n'est pas recommandé pour l'automatisation du navigateur", "page_changed_checking_auth": "Page modifi<PERSON>, vérifiant l'authentique ...", "please_select_your_google_account_to_continue": "Veuillez sélectionner votre compte Google pour continuer ...", "browser_setup_failed": "La configuration du navigateur a échoué: {erreur}", "missing_authentication_data": "Données d'authentification manquantes: {data}", "using_configured_browser_path": "Utilisation du chemin configuré {<PERSON><PERSON><PERSON>}: {path}", "could_not_find_usage_count": "Impossible de trouver le nombre d'utilisation: {error}", "killing_browser_processes": "<PERSON><PERSON> {<PERSON><PERSON><PERSON>} Processus ...", "account_has_reached_maximum_usage": "Le compte a atteint une utilisation maximale, {Suppression}", "browser_setup_completed": "Configuration du navigateur terminé avec succès", "could_not_find_email": "Impossible de trouver un e-mail: {error}", "user_data_dir_not_found": "{Browser} Répertoire des données utilisateur introuvable sur {path}, essaiera à la place Chrome", "found_browser_user_data_dir": "Found {Browser} Répertoire des données utilisateur: {path}", "invalid_authentication_type": "Type d'authentification non valide"}, "manual_auth": {"auth_type_selected": "Type d'authentification sélectionné: {type}", "proceed_prompt": "Procéder? (O / N):", "auth_type_github": "<PERSON><PERSON><PERSON>", "invalid_token": "Jeton non valide. Authentification abandonnée.", "confirm_prompt": "Veuillez confirmer les informations suivantes:", "continue_anyway": "Continuer de toute façon? (O / N):", "token_verified": "Token vérifié avec succès!", "error": "Erreur: {<PERSON><PERSON><PERSON>}", "auth_update_failed": "Échec de la mise à jour des informations d'authentification", "auth_type_prompt": "Sélectionnez le type d'authentification:", "auth_type_auth0": "Auth_0 (par défaut)", "verifying_token": "Vérification de la validité des jetons ...", "auth_updated_successfully": "Informations sur l'authentification mises à jour avec succès!", "email_prompt": "Entrez le courrier électronique (laissez en blanc pour un e-mail aléatoire):", "token_prompt": "Entrez votre jeton de curseur (Access_token / Refresh_token):", "title": "Authentification man<PERSON><PERSON> du <PERSON>ur", "token_verification_skipped": "Vérification des jetons sautés (Check_User_Authorized.py INTORST)", "random_email_generated": "Email aléatoire géné<PERSON>: {email}", "token_required": "Le jeton est requis", "auth_type_google": "Google", "operation_cancelled": "Opération annulée", "token_verification_error": "Erreur Vérification du jeton: {<PERSON><PERSON>ur}", "updating_database": "Mise à jour de la base de données d'authentification du curseur ..."}, "auth_check": {"token_length": "Longueur de jeton: {longueur} caractères", "usage_response_status": "État de la réponse d'utilisation: {réponse}", "operation_cancelled": "Opération annulée par l'utilisateur", "error_getting_token_from_db": "Erreur d'obtention de jetons à partir de la base de données: {erreur}", "checking_usage_information": "Vérification des informations d'utilisation ...", "usage_response": "Réponse d'utilisation: {réponse}", "authorization_failed": "L'autorisation a échoué!", "authorization_successful": "Autorisation réussie!", "check_error": "Autorisation de vérification des erreurs: {erreur}", "request_timeout": "<PERSON><PERSON><PERSON> de chronométrage", "connection_error": "Erreur de connexion", "invalid_token": "Jeton non valide", "enter_token": "Entrez votre jeton de curseur:", "check_usage_response": "Vérifiez la réponse à l'utilisation: {réponse}", "token_found_in_db": "Jeton trouvé dans la base de données", "user_unauthorized": "L'utilisateur n'est pas autorisé", "checking_authorization": "Vérification de l'autorisation ...", "error_generating_checksum": "<PERSON><PERSON><PERSON> générant la somme de contrôle: {error}", "unexpected_error": "Erreur inattendue: {erreur}", "token_source": "Obtenir des jetons à partir de la base de données ou des entrées manuellement? (d / m, par défaut: d)", "user_authorized": "L'utilisateur est autorisé", "token_not_found_in_db": "Jeton introuvable dans la base de données", "jwt_token_warning": "Le jeton semble être au format JWT, mais API Check a renvoyé un code d'état inattendu. Le jeton peut être valide mais l'accès à l'API est restreint.", "unexpected_status_code": "Code d'état inattendu: {code}", "getting_token_from_db": "Obtenir des jetons de la base de données ...", "cursor_acc_info_not_found": "cursor_acc_info.py introuvable"}, "account_delete": {"delete_input_not_found": "Supprimer l'entrée de confirmation non trouvée après plusieurs tentatives", "confirm_button_not_found": "Bouton de confirmation introuvable après plusieurs tentatives", "logging_in": "Connexion avec Google ...", "confirm_button_error": "Bouton de confirmation de recherche d'erreur: {erreur}", "delete_button_clicked": "Cliquez sur le bouton Supprimer le compte", "confirm_prompt": "Êtes-vous sûr de vouloir procéder? (O / N):", "delete_button_error": "Erreur Recherche du bouton de suppression: {Erreur}", "cancelled": "Suppression du compte annulé.", "interrupted": "Processus de suppression du compte interrompu par l'utilisateur.", "error": "Erreur pendant la suppression du compte: {erreur}", "delete_input_not_found_continuing": "Supprimer l'entrée de confirmation non trouvée, essayant de continuer de toute façon", "advanced_tab_retry": "On<PERSON>t avancé non trouvé, tentative {tentative} / {max_attempts}", "waiting_for_auth": "En attendant l'authentification Google ...", "typed_delete": "<PERSON><PERSON><PERSON> \"Supprimer\" dans la boîte de confirmation", "trying_settings": "Essayer de naviguer vers la page des paramètres ...", "delete_input_retry": "Supprimer l'entrée introuvable, tentative {tentative} / {max_attempts}", "email_not_found": "E-mail introuvable: {error}", "delete_button_not_found": "Supprimer le bouton du compte introuvable après plusieurs tentatives", "already_on_settings": "Déjà sur la page des paramètres", "failed": "Le processus de suppression du compte a échoué ou a été annulé.", "warning": "AVERTISSEMENT: <PERSON>la supprimera en permanence votre compte de curseur. Cette action ne peut pas être annulée.", "direct_advanced_navigation": "Essayer la navigation directe vers l'onglet avancé", "advanced_tab_not_found": "Onglet avancé non trouvé après plusieurs tentatives", "auth_timeout": "Timeout d'authentification, continuant de toute façon ...", "select_google_account": "Veuillez sélectionner votre compte Google ...", "google_button_not_found": "Bouton de connexion Google introuvable", "found_danger_zone": "Section de la zone de danger trouvée", "account_deleted": "Compte supprimé avec succès!", "starting_process": "Processus de suppression du compte de départ ...", "advanced_tab_error": "Erreur Recherche d'onglet Avancé: {Erreur}", "delete_button_retry": "Bouton de suppression introuvable, tentative {tentative} / {max_attempts}", "login_redirect_failed": "La redirection de connexion a échoué, essayant la navigation directe ...", "unexpected_error": "Erreur inattendue: {erreur}", "login_successful": "Connectez-vous à succès", "delete_input_error": "E<PERSON>ur Rechercher la suppression de l'entrée: {erreur}", "advanced_tab_clicked": "Cliquez sur l'onglet avancé", "unexpected_page": "Page inattendue après la connexion: {URL}", "found_email": "Email trouvé: {email}", "title": "Outil de suppression du compte Google Cursor Google", "navigating_to_settings": "Navigation vers la page des paramètres ...", "success": "Votre compte Cursor a été supprimé avec succès!", "confirm_button_retry": "Bouton de confirmation introuvable, tentative {tentative} / {max_attempts}"}, "token": {"refreshing": "<PERSON><PERSON> raf<PERSON> ...", "extraction_error": "Erreur Extraction de jeton: {Erreur}", "invalid_response": "Réponse JSON non valide du serveur de rafraîchissement", "no_access_token": "Pas de jeton d'accès en réponse", "connection_error": "Erreur de connexion pour actualiser le serveur", "unexpected_error": "<PERSON><PERSON><PERSON> inattendue lors de la rafraîchissement du jeton: {erreur}", "server_error": "Erreur de serveur de refrex: http {status}", "refresh_success": "Jeton actualisé avec succès! VALIDE pour {jours} jours (expire: {expire})", "request_timeout": "<PERSON><PERSON><PERSON> de refrex sur le serveur", "refresh_failed": "Un rafraîchissement du jeton a échoué: {error}"}, "browser_profile": {"profile_selected": "Profil sélectionné: {Profil}", "default_profile": "Profil par défaut", "no_profiles": "Non {Browser} Profils trouvés", "select_profile": "Sélectionnez le profil {<PERSON><PERSON><PERSON>} à utiliser:", "error_loading": "Erreur Chargement {<PERSON><PERSON><PERSON>} Profils: {Erreur}", "invalid_selection": "Sélection non valide. Veuillez réessayer.", "title": "Sélection de profil de navigateur", "profile": "Profil {numéro}", "profile_list": "Profils {<PERSON><PERSON><PERSON>} disponibles:"}, "github_register": {"feature2": "Enregistre un nouveau compte GitHub avec des informations d'identification aléatoires.", "feature6": "Enregistre toutes les informations d'identification dans un fichier.", "starting_automation": "Automatisation de départ ...", "feature1": "Génère un e-mail temporaire en utilisant 1secmail.", "title": "GitHub + Cursor AI Enregistrement Automatisation", "github_username": "Nom d'utilisateur github", "check_browser_windows_for_manual_intervention_or_try_again_later": "Vérifiez les fenêtres du navigateur pour une intervention manuelle ou réessayer plus tard.", "warning1": "Ce script automatise la création de compte, qui peut violer les conditions d'utilisation GitHub / Cursor.", "feature4": "Se connecte à Cursor AI à l'aide de l'authentification GitHub.", "invalid_choice": "Choix non valide. Veuillez saisir «oui» ou «non»", "completed_successfully": "L'enregistrement GitHub + Cursor s'est terminé avec succès!", "warning2": "Nécessite l'accès à Internet et les privilèges administratifs.", "registration_encountered_issues": "L'enregistrement GitHub + Cursor a rencontré des problèmes.", "credentials_saved": "Ces informations d'identification ont été enregistrées sur github_cursor_accounts.txt", "feature3": "Vérifie automatiquement l'e-mail GitHub.", "github_password": "Mot de passe github", "features_header": "Caractéristiques", "feature5": "Réinitialise l'ID de la machine pour contourner la détection des essais.", "warning4": "Utilisez de manière responsable et à vos risques et périls.", "warning3": "Capcha ou vérification supplémentaire peut interrompre l'automatisation.", "cancelled": "Opération annulée", "warnings_header": "Avertissements", "program_terminated": "Programme terminé par l'utilisateur", "confirm": "Êtes-vous sûr de vouloir procéder?", "email_address": "<PERSON><PERSON><PERSON> email"}, "account_info": {"subscription": "Abonnement", "failed_to_get_account_info": "Échec de l'obtention des informations de compte", "subscription_type": "Type d'abonnement", "pro": "Pro", "failed_to_get_account": "Échec de l'obtention des informations de compte", "config_not_found": "Configuration introuvable.", "premium_usage": "Utilisation premium", "failed_to_get_subscription": "Échec de l'obtention d'informations d'abonnement", "basic_usage": "Utilisation de base", "premium": "Prime", "free": "<PERSON><PERSON><PERSON>", "email_not_found": "E-mail introuvable", "title": "Informations sur le compte", "inactive": "Inactif", "remaining_trial": "<PERSON><PERSON><PERSON> restant", "enterprise": "Entreprise", "lifetime_access_enabled": "Accès à vie activé", "usage_not_found": "Utilisation introuvable", "failed_to_get_usage": "Échec de l'obtention des informations d'utilisation", "days_remaining": "Jours restants", "failed_to_get_token": "Échec du jeton", "token": "<PERSON><PERSON>", "subscription_not_found": "Informations sur l'abonnement introuvables", "days": "jours", "team": "Équipe", "token_not_found": "Jeton introuvable", "active": "Actif", "email": "E-mail", "pro_trial": "Procès professionnel", "failed_to_get_email": "Échec de l'adresse e-mail", "trial_remaining": "Essai professionnel restant", "usage": "Usage"}, "config": {"configuration": "Configuration", "config_updated": "Configuration mise à jour", "file_owner": "Pro<PERSON><PERSON><PERSON><PERSON>: {proprié<PERSON>}", "error_checking_linux_paths": "Erreur Vérification des chemins Linux: {Erreur}", "storage_file_is_empty": "Le fichier de stockage est vide: {Storage_Path}", "config_directory": "Répertoire de configuration", "documents_path_not_found": "Documents Path introuvable, en utilisant le répertoire actuel", "config_not_available": "Configuration non disponible", "please_make_sure_cursor_is_installed_and_has_been_run_at_least_once": "Veuillez vous assurer que le curseur est installé et a été exécuté au moins une fois", "neither_cursor_nor_cursor_directory_found": "Ni le répertoire du curseur ni du curseur trouvé dans {config_base}", "config_created": "Configré créé: {config_file}", "using_temp_dir": "Utilisation du répertoire temporaire en raison de l'erreur: {path} (erreur: {erreur})", "storage_file_not_found": "Fichier de stockage introuvable: {Storage_Path}", "the_file_might_be_corrupted_please_reinstall_cursor": "Le fichier peut être corrompu, ve<PERSON><PERSON><PERSON> r<PERSON><PERSON><PERSON><PERSON> le curseur", "error_getting_file_stats": "Erreur d'obtention des statistiques de fichiers: {erreur}", "enabled": "Activé", "backup_created": "<PERSON>uvegarde cré<PERSON>: {Path}", "file_permissions": "Autorisations de fichiers: {autorisations}", "config_setup_error": "Configuration de la configuration d'erreur: {erreur}", "config_force_update_enabled": "Config File Force Update activé, effectuer une mise à jour forcée", "config_removed": "Fichier de configuration supprimé pour la mise à jour forcée", "file_size": "<PERSON><PERSON>: {taille} octets", "error_reading_storage_file": "Erreur de lecture du fichier de stockage: {erreur}", "config_force_update_disabled": "Config File Force Update désactivé, saut à la mise à jour forcée", "config_dir_created": "Répertoire de configuration créé: {path}", "config_option_added": "Option de configuration ajoutée: {Option}", "file_group": "Groupe de fichiers: {groupe}", "and": "Et", "backup_failed": "Impossible de sauvegarder la configuration: {error}", "force_update_failed": "Force la configuration de mise à jour défaillante: {error}", "storage_directory_not_found": "Répertoire de stockage introuvable: {Storage_dir}", "also_checked": "Également vérifié {path}", "disabled": "Désactivé", "storage_file_found": "Fichier de stockage trouvé: {Storage_Path}", "try_running": "Essayez de courir: {Commande}", "storage_file_is_valid_and_contains_data": "Le fichier de stockage est valide et contient des données", "permission_denied": "Permission refusée: {Storage_Path}"}, "bypass": {"found_product_json": "Trouvé produit.json: {path}", "starting": "Démarrage de la version du curseur Tytrass ...", "version_updated": "Version mise à jour de {old} à {new}", "menu_option": "Contourner la version de la version du curseur", "unsupported_os": "Système d'exploitation non pris en charge: {Système}", "backup_created": "<PERSON>uvegarde cré<PERSON>: {Path}", "current_version": "Version actuelle: {version}", "localappdata_not_found": "Variable d'environnement localappdata introuvable", "no_write_permission": "Aucune autorisation d'écriture pour le fichier: {path}", "write_failed": "Échec de l'écriture de produit.json: {error}", "description": "Cet outil modifie le produit de Cursor.json pour contourner les restrictions de version", "bypass_failed": "Version Bypass a échoué: {<PERSON><PERSON><PERSON>}", "title": "<PERSON>il de contournement de la version du curseur", "no_update_needed": "Aucune mise à jour nécessaire. La version actuelle {version} est déjà> = 0,46.0", "read_failed": "Échec de la lecture de Product.json: {error}", "stack_trace": "Trace de pile", "product_json_not_found": "product.json introuvable dans les chemins linux communs", "file_not_found": "Fichier introuvable: {Path}"}, "bypass_token_limit": {"description": "Cet outil modifie le fichier workbench.desktop.main.js pour contourner la limite de jeton", "press_enter": "Appuyez sur Entrée pour continuer ...", "title": "Outil de limite de jeton de contournement"}, "tempmail": {"no_email": "Aucun e-mail de vérification du curseur trouvé", "general_error": "Une erreur s'est produite: {erreur}", "config_error": "Erreur de fichier de configuration: {erreur}", "configured_email": "Email configuré: {e-mail}", "extract_code_failed": "Extraire le code de vérification a échoué: {error}", "checking_email": "Vérification du courrier électronique de vérification du curseur ...", "email_found": "Email de vérification du curseur trouvé", "no_code": "Impossible d'obtenir le code de vérification", "check_email_failed": "Vérifier l'échec de l'e-mail: {Erreur}", "verification_code": "Code de vérification: {code}"}}