{"menu": {"title": "Opções Disponíveis", "exit": "Sair do Programa", "reset": "Redefinir ID da Máquina", "register": "Registrar Nova Conta no Cursor", "register_google": "Registrar com Conta do Google", "register_github": "Registrar com Conta do GitHub", "register_manual": "Registrar Cursor com E-mail Personalizado", "quit": "<PERSON><PERSON><PERSON>", "select_language": "Alterar I<PERSON>", "input_choice": "Por favor, insira sua escolha ({choices})", "invalid_choice": "Se<PERSON>ção inválida. Insira um número de {choices}", "program_terminated": "Programa encerrado pelo usuário", "error_occurred": "Ocorreu um erro: {error}. Por favor, tente novamente", "press_enter": "Pressione Enter para Sair", "disable_auto_update": "Desativar Atualização Automática do Cursor", "lifetime_access_enabled": "ACESSO VITALÍCIO HABILITADO", "totally_reset": "Redefinir <PERSON>", "outdate": "Obsoleto", "temp_github_register": "Registro temporário do GitHub", "coming_soon": "Em breve", "fixed_soon": "Será corrigido em breve", "contribute": "Contribuir para o Projeto", "config": "Mostrar Configuração", "delete_google_account": "Excluir Conta Google do Cursor", "continue_prompt": "Continuar? (y/N): ", "operation_cancelled_by_user": "Operação cancelada pelo usuário", "exiting": "Saindo ......", "bypass_version_check": "Ignorar Verificação de Versão do Cursor", "check_user_authorized": "Verificar Autorização do Usuário", "bypass_token_limit": "<PERSON><PERSON><PERSON>", "restore_machine_id": "Restaurar ID da Máquina do Backup", "select_chrome_profile": "Selecione o perfil do Chrome", "admin_required": "Em execução como executável, os privilégios do administrador são necessários.", "language_config_saved": "Configuração do idioma economizou com sucesso", "lang_invalid_choice": "Escolha inválida. Por favor, insira uma das seguintes opções: ({Lang_Choices})", "manual_custom_auth": "Auth personalizado manual", "admin_required_continue": "Continuando sem privilégios de administrador."}, "languages": {"ar": "<PERSON><PERSON><PERSON>", "en": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zh_cn": "<PERSON><PERSON><PERSON>li<PERSON>", "zh_tw": "<PERSON>ês Tradici<PERSON>", "vi": "Vietnamita", "nl": "<PERSON><PERSON><PERSON><PERSON>", "de": "Alemão", "fr": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pt": "Português do Brasil", "ru": "<PERSON>", "es": "Espanhol", "bg": "b<PERSON><PERSON><PERSON>", "tr": "turco", "ja": "j<PERSON><PERSON><PERSON>", "it": "italiano"}, "quit_cursor": {"start": "Iniciando fechamento do Cursor", "no_process": "Nenhum processo do Cursor em execução", "terminating": "Encerrando processo {pid}", "waiting": "Aguardando o processo ser finalizado", "success": "Todos os processos do Cursor foram encerrados", "timeout": "Tempo limite do processo: {pids}", "error": "Ocorreu um erro: {error}"}, "reset": {"title": "Ferramenta de Redefinição de ID da Máquina", "checking": "Verificando arquivo de configuração", "not_found": "Arquivo de configuração não encontrado", "no_permission": "Não é possível ler ou escrever no arquivo de configuração. Verifique as permissões do arquivo", "reading": "<PERSON>do configuração atual", "creating_backup": "<PERSON><PERSON><PERSON> backup da configuração", "backup_exists": "Arquivo de backup já existe, pulando etapa de backup", "generating": "Gerando novo ID da máquina", "saving_json": "Salvando nova configuração no JSON", "success": "ID da Máquina redefinido com sucesso", "new_id": "Novo ID da Máquina", "permission_error": "<PERSON>rro de permis<PERSON>: {error}", "run_as_admin": "Tente executar este programa como Administrador", "process_error": "Erro no processo de redefinição: {error}", "updating_sqlite": "Atualizando banco de dados SQLite", "updating_pair": "Atualizando chave-valor", "sqlite_success": "Banco de dados SQLite atualizado com sucesso", "sqlite_error": "Falha na atualização do banco de dados SQLite: {error}", "press_enter": "Pressione Enter para sair", "unsupported_os": "Sistema operacional não suportado: {os}", "linux_path_not_found": "Caminho do Linux não encontrado", "updating_system_ids": "Atualizando IDs do sistema", "system_ids_updated": "IDs do sistema atualizados com sucesso", "system_ids_update_failed": "Falha na atualização dos IDs do sistema: {error}", "windows_guid_updated": "GUID do Windows atualizado com sucesso", "windows_permission_denied": "Permissão negada no Windows", "windows_guid_update_failed": "Falha na atualização do GUID do Windows", "macos_uuid_updated": "UUID do macOS atualizado com sucesso", "plutil_command_failed": "Falha no comando plutil", "start_patching": "Iniciando correção de getMachineId", "macos_uuid_update_failed": "Falha na atualização do UUID do macOS", "current_version": "Versão atual do Cursor: {version}", "patch_completed": "Correção de getMachineId concluída", "patch_failed": "Falha na correção de getMachineId: {error}", "version_check_passed": "Verificação de versão do Cursor aprovada", "file_modified": "Arquivo modificado", "version_less_than_0_45": "Versão do Cursor < 0.45.0, pulando correção de getMachineId", "detecting_version": "Detectando versão do Cursor", "patching_getmachineid": "Corrigindo getMachineId", "version_greater_than_0_45": "Versão do Cursor >= 0.45.0, corrigindo getMachineId", "permission_denied": "Permissão negada: {error}", "backup_created": "Backup criado", "update_success": "Atualização concluída com sucesso", "update_failed": "Falha na atualização: {error}", "windows_machine_guid_updated": "GUID da máquina do Windows atualizado com sucesso", "reading_package_json": "Lendo package.json {path}", "invalid_json_object": "Objeto JSON inválido", "no_version_field": "Campo de versão não encontrado no package.json", "version_field_empty": "Campo de versão está vazio", "invalid_version_format": "Formato de versão inválido: {version}", "found_version": "Versão encontrada: {version}", "version_parse_error": "Erro ao analisar versão: {error}", "package_not_found": "Package.json não encontrado: {path}", "check_version_failed": "Falha ao verificar versão: {error}", "stack_trace": "Rastreamento de pilha", "version_too_low": "Versão do Cursor muito baixa: {version} < 0.45.0", "update_windows_machine_id_failed": "Atualizar o ID do Windows Machine falhou: {Error}", "windows_machine_id_updated": "ID da máquina do Windows atualizado com sucesso", "path_not_found": "Caminho não encontrado: {caminho}", "update_windows_machine_guid_failed": "Atualizar o Windows Machine Guid falhou: {Error}", "no_write_permission": "Sem permissão de gravação: {caminho}", "file_not_found": "Arquivo não encontrado: {caminho}", "modify_file_failed": "Modificar o arquivo falhado: {erro}"}, "register": {"title": "Ferramenta de Registro do Cursor", "start": "Iniciando o processo de registro...", "handling_turnstile": "Processando verificação de segurança...", "retry_verification": "Tentando novamente a verificação...", "detect_turnstile": "Verificando validação de segurança...", "verification_success": "Verificação de segurança bem-sucedida", "starting_browser": "<PERSON><PERSON>ndo navegador...", "form_success": "Formulário enviado com sucesso", "browser_started": "Navegador aberto com sucesso", "waiting_for_second_verification": "Aguardando verificação por e-mail...", "waiting_for_verification_code": "Aguardando código de verificação...", "password_success": "Senha definida com sucesso", "password_error": "<PERSON>ão foi possível definir a senha: {error}. Por favor, tente novamente", "waiting_for_page_load": "Carregando página...", "first_verification_passed": "Verificação inicial bem-sucedida", "mailbox": "Caixa de entrada de e-mail acessada com sucesso", "register_start": "Iniciar <PERSON>", "form_submitted": "Formul<PERSON><PERSON>, Iniciando Verificação...", "filling_form": "<PERSON><PERSON><PERSON><PERSON>", "visiting_url": "Visitando URL", "basic_info": "Informações básicas enviadas", "handle_turnstile": "Processar Turnstile", "no_turnstile": "Turnstile <PERSON> Detectado", "turnstile_passed": "Turnst<PERSON>", "verification_start": "Iniciando Obtenção do Código de Verificação", "verification_timeout": "Tempo Esgotado para Obter Código de Verificação", "verification_not_found": "Nenhum Código de Verificação Encontrado", "try_get_code": "Tentativa | {attempt} Obter Código de Verificação | Tempo Restante: {time}s", "get_account": "Obtendo Informações da Conta", "get_token": "Obtendo Token da Sessão do Cursor", "token_success": "Token Obtido com Sucesso", "token_attempt": "Tentativa | {attempt} de obter o Token | Tentando novamente em {time}s", "token_max_attempts": "Número máximo de tentativas atingido ({max}) | Falha ao obter o Token", "token_failed": "Falha ao Obter <PERSON>: {error}", "account_error": "Falha ao Obter Informações da Conta: {error}", "press_enter": "Pressione Enter para sair", "browser_start": "<PERSON><PERSON><PERSON><PERSON>", "open_mailbox": "A<PERSON>ndo Página da Caixa de Entrada", "email_error": "Falha ao obter endereço de e-mail", "setup_error": "Erro de configuração do e-mail: {error}", "start_getting_verification_code": "Iniciando obtenção do código de verificação, tentará em 60s", "get_verification_code_timeout": "Tempo Esgotado para Obter Código de Verificação", "get_verification_code_success": "Código de Verificação Obtido com Sucesso", "try_get_verification_code": "Tentativa | {attempt} Obter Código de Verificação | Tempo Restante: {remaining_time}s", "verification_code_filled": "Código de Verificação Preenchido", "login_success_and_jump_to_settings_page": "Login bem-sucedido, indo para a página de configurações", "detect_login_page": "Página de login detectada, iniciando login...", "cursor_registration_completed": "Registro do Cursor Concluído!", "set_password": "<PERSON><PERSON><PERSON>", "basic_info_submitted": "Informações Básicas Enviadas", "cursor_auth_info_updated": "Informações de Autenticação do Cursor Atualizadas", "cursor_auth_info_update_failed": "Falha ao Atualizar Informações de Autenticação do Cursor", "reset_machine_id": "Reiniciar ID da Máquina", "account_info_saved": "Informações da Conta Salvas", "save_account_info_failed": "Falha ao Salvar Informações da Conta", "get_email_address": "Obtendo Endereço de E-mail", "update_cursor_auth_info": "Atualizar Informações de Autenticação do Cursor", "register_process_error": "<PERSON>rro no Processo de Registro: {error}", "setting_password": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "manual_code_input": "Inserção Manual do Código", "manual_email_input": "Inserção Manual de E-mail", "password": "<PERSON><PERSON>", "first_name": "Nome", "last_name": "Sobrenome", "exit_signal": "<PERSON><PERSON> para Sai<PERSON>", "email_address": "Endereço de E-mail", "config_created": "Configuração Criada", "verification_failed": "Falha na Verificação", "verification_error": "Erro de Verificação: {error}", "config_option_added": "Opção de Configuração Adicionada: {option}", "config_updated": "Configuração Atualizada", "password_submitted": "<PERSON><PERSON>", "total_usage": "Uso Total: {usage}", "setting_on_password": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getting_code": "Obtendo Código de Verificação, Tentará em 60s", "using_browser": "Usando {navegador} navegador: {caminho}", "could_not_track_processes": "Não foi possível rastrear {navegador} processos: {error}", "try_install_browser": "Tente instalar o navegador com seu gerenciador de pacotes", "tempmail_plus_verification_started": "Processo de verificação de tempmailplus inicial", "max_retries_reached": "Tentativas máximas de repetição alcançadas. O registro falhou.", "tempmail_plus_enabled": "Tempmailplus está ativado", "browser_path_invalid": "{navegador} O caminho é inválido, usando o caminho padrão", "human_verify_error": "Não é possível verificar se o usuário é humano. Representando ...", "using_tempmail_plus": "Usando o tempmailplus para verificação de email", "tracking_processes": "Rastreamento {count} {navegador} processos", "tempmail_plus_epin_missing": "Tempmailplus epin não está configurado", "tempmail_plus_verification_failed": "Verificação do tempmailplus falhou: {error}", "using_browser_profile": "Usando {navegador} perfil de: {user_data_dir}", "tempmail_plus_verification_completed": "Verificação de tempmailplus concluída com êxito", "tempmail_plus_email_missing": "O e -mail tempmailplus não está configurado", "tempmail_plus_config_missing": "Falta a configuração tempmailplus", "tempmail_plus_init_failed": "Falha ao inicializar o tempmailplus: {error}", "tempmail_plus_initialized": "Tempmailplus inicializou com sucesso", "tempmail_plus_disabled": "Tempmailplus está desativado", "no_new_processes_detected": "Nenhum novo {navegador} processos detectados para rastrear", "make_sure_browser_is_properly_installed": "Verifique se {navegador} está instalado corretamente"}, "auth": {"title": "Gerenciador de Autenticação do Cursor", "checking_auth": "Verificando arquivo de autenticação", "auth_not_found": "Arquivo de autenticação não encontrado", "auth_file_error": "Erro no arquivo de autenticação: {error}", "reading_auth": "Lendo arquivo de autenticação", "updating_auth": "Atualizando informações de autenticação", "auth_updated": "Informações de autenticação atualizadas com sucesso", "auth_update_failed": "Falha ao atualizar informações de autenticação: {error}", "auth_file_created": "Arquivo de autenticação criado", "auth_file_create_failed": "Falha ao criar arquivo de autenticação: {error}", "press_enter": "Pressione Enter para sair", "reset_machine_id": "Redefinir ID da máquina", "database_connection_closed": "Conexão com o banco de dados fechada", "database_updated_successfully": "Banco de dados atualizado com sucesso", "connected_to_database": "Conectado ao banco de dados", "updating_pair": "Atualizando par chave-valor", "db_not_found": "Arquivo do banco de dados não encontrado em: {path}", "db_permission_error": "Não é possível acessar o arquivo do banco de dados. Verifique as permissões", "db_connection_error": "Falha ao conectar ao banco de dados: {error}"}, "control": {"generate_email": "Gerando novo e-mail", "blocked_domain": "<PERSON><PERSON><PERSON> blo<PERSON>", "select_domain": "<PERSON><PERSON><PERSON><PERSON><PERSON> do<PERSON>ató<PERSON>", "copy_email": "Copiando endereço de e-mail", "enter_mailbox": "Entrando na caixa de entrada", "refresh_mailbox": "Atualizando caixa de entrada", "check_verification": "Verificando código de verificação", "verification_found": "Código de verificação encontrado", "verification_not_found": "Nenhum código de verificação encontrado", "browser_error": "Erro no controle do navegador: {error}", "navigation_error": "<PERSON>rro de navegação: {error}", "email_copy_error": "Erro ao copiar e-mail: {error}", "mailbox_error": "Erro na caixa de entrada: {error}", "token_saved_to_file": "Token salvo em cursor_tokens.txt", "navigate_to": "Navegando para {url}", "generate_email_success": "E-mail gerado com sucesso", "select_email_domain": "Selecionar domínio de e-mail", "select_email_domain_success": "Domínio de e-mail selecionado com sucesso", "get_email_name": "Obtendo nome do e-mail", "get_email_name_success": "Nome do e-mail obtido com sucesso", "get_email_address": "Obtendo endereço de e-mail", "get_email_address_success": "Endereço de e-mail obtido com sucesso", "enter_mailbox_success": "Entrada na caixa de entrada bem-sucedida", "found_verification_code": "Código de verificação encontrado", "get_cursor_session_token": "Obtendo token da sessão do Cursor", "get_cursor_session_token_success": "Token da sessão do Cursor obtido com sucesso", "get_cursor_session_token_failed": "Falha ao obter token da sessão do Cursor", "save_token_failed": "<PERSON>alha ao salvar o token", "database_updated_successfully": "Banco de dados atualizado com sucesso", "database_connection_closed": "Conexão com o banco de dados fechada", "no_valid_verification_code": "Nenhum código de verificação válido"}, "email": {"starting_browser": "<PERSON><PERSON><PERSON><PERSON>", "visiting_site": "Visitando <PERSON> de e-mail", "create_success": "E-mail criado com sucesso", "create_failed": "Falha ao criar e-mail", "create_error": "Erro ao criar e-mail: {error}", "refreshing": "Atualizando e-mail", "refresh_success": "E-mail atualizado com sucesso", "refresh_error": "Erro ao atualizar e-mail: {error}", "refresh_button_not_found": "Botão de atualização não encontrado", "verification_found": "Verificação encontrada", "verification_not_found": "Verificação não encontrada", "verification_error": "Erro na verificação: {error}", "verification_code_found": "Código de verificação encontrado", "verification_code_not_found": "Código de verificação não encontrado", "verification_code_error": "Erro no código de verificação: {error}", "address": "Endereço de e-mail", "all_domains_blocked": "Todos os domínios bloqueado<PERSON>, alternando servi<PERSON>o", "no_available_domains_after_filtering": "Nenhum domínio disponível após filtragem", "switching_service": "Alternando para o serviço {service}", "domains_list_error": "Falha ao obter lista de domínios: {error}", "failed_to_get_available_domains": "Falha ao obter domínios disponíveis", "domains_excluded": "Domínios excluídos: {domains}", "failed_to_create_account": "Falha ao criar conta", "account_creation_error": "Erro na criação da conta: {error}", "blocked_domains": "<PERSON><PERSON>ios bloqueados: {domains}", "blocked_domains_loaded": "<PERSON><PERSON><PERSON> blo<PERSON> carregados: {count}", "blocked_domains_loaded_error": "Erro ao carregar domínios bloqueados: {error}", "blocked_domains_loaded_success": "<PERSON><PERSON><PERSON> bloqueados carregados com sucesso", "blocked_domains_loaded_timeout": "Tempo esgotado ao carregar domínios bloqueados: {timeout}s", "blocked_domains_loaded_timeout_error": "Erro de tempo esgotado ao carregar domínios bloqueados: {error}", "available_domains_loaded": "<PERSON><PERSON><PERSON> disponí<PERSON><PERSON> carregado<PERSON>: {count}", "domains_filtered": "<PERSON><PERSON><PERSON>: {count}", "trying_to_create_email": "Ten<PERSON>do criar e-mail: {email}", "domain_blocked": "<PERSON><PERSON><PERSON> blo<PERSON>: {domain}", "no_display_found": "Nenhuma tela encontrada. Verifique se o X servidor está em execução.", "try_export_display": "Tente: exportar exibição =: 0", "try_install_chromium": "Tente: sudo apt install-navegador de cromo", "extension_load_error": "Erro de carga de extensão: {erro}", "make_sure_chrome_chromium_is_properly_installed": "Verifique se o Chrome/Chromium está instalado corretamente", "using_chrome_profile": "Usando o perfil do Chrome de: {user_data_dir}"}, "update": {"title": "Desativar atualização automática do Cursor", "disable_success": "Atualização automática desativada com sucesso", "disable_failed": "Falha ao desativar atualização automática: {error}", "press_enter": "Pressione Enter para sair", "start_disable": "Iniciando desativação da atualização automática", "killing_processes": "Finalizando processos", "processes_killed": "Processos finalizados", "removing_directory": "<PERSON><PERSON><PERSON><PERSON>", "directory_removed": "<PERSON>ret<PERSON><PERSON> removido", "creating_block_file": "<PERSON><PERSON><PERSON> arqui<PERSON> de blo<PERSON>", "block_file_created": "Arquivo de bloqueio criado", "clearing_update_yml": "Limpeza update.yml arquivo", "update_yml_cleared": "arquivo update.yml limpo", "unsupported_os": "OS não suportado: {System}", "block_file_already_locked": "O arquivo de bloco já está bloqueado", "yml_already_locked_error": "arquivo update.yml já bloqueado erro: {error}", "update_yml_not_found": "arquivo update.yml não encontrado", "yml_locked_error": "Update.yml Arquivo Bloqueado Erro: {Error}", "remove_directory_failed": "Falha ao remover o diretório: {error}", "yml_already_locked": "o arquivo update.yml já está bloqueado", "create_block_file_failed": "Falha ao criar o arquivo de bloco: {error}", "block_file_locked_error": "Erro bloqueado do arquivo bloqueado: {error}", "directory_locked": "O diretório está bloqueado: {caminho}", "block_file_already_locked_error": "Bloco Arquivo já bloqueado Erro: {Error}", "clear_update_yml_failed": "Falha ao limpar o arquivo update.yml: {error}", "yml_locked": "o arquivo update.yml está bloqueado", "block_file_locked": "O arquivo de bloco está bloqueado"}, "updater": {"checking": "Verificando atualizações...", "new_version_available": "Nova versão disponível! (Atual: {current}, Última: {latest})", "updating": "Atualizando para a última versão. O programa será reiniciado automaticamente.", "up_to_date": "Você está usando a versão mais recente.", "check_failed": "Falha ao verificar atualizações: {error}", "continue_anyway": "Continuando com a versão atual...", "update_confirm": "Deseja atualizar para a última versão? (Y/n)", "update_skipped": "Atualização ignorada.", "invalid_choice": "Escolha inválida. Por favor, digite 'Y' ou 'n'.", "development_version": "Versão de desenvolvimento {current} > {latest}", "changelog_title": "Registro de alterações", "rate_limit_exceeded": "Limite de taxa de API do GitHub excedido. Saltando a verificação de atualização."}, "totally_reset": {"title": "Redefinir <PERSON>", "checking_config": "Verificando Arquivo de Configuração", "config_not_found": "Arquivo de Configuração Não Encontrado", "no_permission": "Não é possível Ler ou Escrever o Arquivo de Configuração, Verifique as Permissões do Arquivo", "reading_config": "Lendo Configuração Atual", "creating_backup": "<PERSON><PERSON><PERSON> Configuração", "backup_exists": "Arquivo de Backup Já Existe, Pulando Etapa de Backup", "generating_new_machine_id": "Gerando Novo ID da Máquina", "saving_new_config": "Salvando Nova Configuração no JSON", "success": "Cursor Redefinido com Sucesso", "error": "Falha ao Redefinir Cursor: {error}", "press_enter": "Pressione Enter para Sair", "reset_machine_id": "Redefinir ID da Máquina", "database_connection_closed": "Conexão com o Banco de Dados Fechada", "database_updated_successfully": "Banco de Dados Atualizado com Sucesso", "connected_to_database": "Conectado ao Banco de Dados", "updating_pair": "Atualizando Par Chave-Valor", "db_not_found": "Arquivo de banco de dados não encontrado em: {path}", "db_permission_error": "Não é possível acessar o arquivo do banco de dados. Verifique as permissões", "db_connection_error": "Falha ao conectar ao banco de dados: {error}", "feature_title": "RECURSOS", "feature_1": "Remoção completa das configurações e preferências do Cursor AI", "feature_2": "Limpa todos os dados em cache, incluindo histórico e prompts de IA", "feature_3": "Redefine o ID da máquina para contornar a detecção de período de teste", "feature_4": "Cria novos identificadores de máquina aleatórios", "feature_5": "Remove extensões e preferências personalizadas", "feature_6": "Redefine informações de período de teste e dados de ativação", "feature_7": "Varredura profunda por arquivos ocultos relacionados à licença e período de teste", "feature_8": "Preserva com segurança arquivos e aplicativos não relacionados ao Cursor", "feature_9": "Compatível com Windows, macOS e Linux", "disclaimer_title": "AVISO", "disclaimer_1": "Esta ferramenta excluirá permanentemente todas as configurações,", "disclaimer_2": "preferências e dados em cache do Cursor AI. Essa ação não pode ser desfeita.", "disclaimer_3": "Seus arquivos de código NÃO serão afetados, e a ferramenta é projetada", "disclaimer_4": "para atingir somente os arquivos do editor Cursor AI e mecanismos de detecção de teste.", "disclaimer_5": "Outros aplicativos em seu sistema não serão afetados.", "disclaimer_6": "Será necessário configurar o Cursor AI novamente após executar esta ferramenta.", "disclaimer_7": "Use por sua conta e risco", "confirm_title": "Tem certeza que deseja prosseguir?", "confirm_1": "Esta ação excluirá todas as configurações do Cursor AI,", "confirm_2": "preferências e dados em cache. Essa ação não pode ser desfeita.", "confirm_3": "Seus arquivos de código NÃO serão afetados, e a ferramenta é projetada", "confirm_4": "para atingir somente os arquivos do editor Cursor AI e mecanismos de detecção de teste.", "confirm_5": "Outros aplicativos em seu sistema não serão afetados.", "confirm_6": "Será necessário configurar o Cursor AI novamente após executar esta ferramenta.", "confirm_7": "Use por sua conta e risco", "invalid_choice": "Por favor, digite 'Y' ou 'n'", "skipped_for_safety": "<PERSON><PERSON><PERSON> por segurança (não relacionado ao Cursor): {path}", "deleted": "Excluído: {path}", "error_deleting": "Erro ao excluir {path}: {error}", "not_found": "Arquivo não encontrado: {path}", "resetting_machine_id": "Redefinindo identificadores da máquina para contornar a detecção de período de teste...", "created_machine_id": "Novo ID da máquina criado: {path}", "error_creating_machine_id": "Erro ao criar arquivo de ID da máquina {path}: {error}", "error_searching": "Erro ao procurar arquivos em {path}: {error}", "created_extended_trial_info": "Novas informações de período de teste criadas: {path}", "error_creating_trial_info": "Erro ao criar arquivo de informações de teste {path}: {error}", "resetting_cursor_ai_editor": "Redefinindo Editor Cursor AI... Por favor, aguarde.", "reset_cancelled": "Redefinição cancelada. Saindo sem realizar alterações.", "windows_machine_id_modification_skipped": "Modificação de ID da máquina no Windows ignorada: {error}", "linux_machine_id_modification_skipped": "Modificação do machine-id do Linux ignorada: {error}", "note_complete_machine_id_reset_may_require_running_as_administrator": "Nota: Redefinir totalmente o ID da máquina pode exigir a execução como administrador", "note_complete_system_machine_id_reset_may_require_sudo_privileges": "Nota: Redefinir totalmente o machine-id do sistema pode exigir privilégios sudo", "windows_registry_instructions": "📝 NOTA: Para uma redefinição completa no Windows, talvez você precise também limpar entradas do registro.", "windows_registry_instructions_2": "   Execute 'regedit', pesquise chaves contendo '<PERSON><PERSON><PERSON>' ou 'CursorAI' em HKEY_CURRENT_USER\\Software\\ e exclua-as.\n", "reset_log_1": "Cursor AI foi completamente redefinido e a detecção de teste foi contornada!", "reset_log_2": "Por favor, reinicie o sistema para que as alterações tenham efeito.", "reset_log_3": "Você precisará reinstalar o Cursor AI e deverá ter um novo período de teste disponível.", "reset_log_4": "Para melhores resultados, considere também:", "reset_log_5": "Utilizar um endereço de e-mail diferente ao registrar um novo período de teste", "reset_log_6": "Se disponível, utilizar uma VPN para alterar seu endereço IP", "reset_log_7": "Limpar cookies e cache do navegador antes de acessar o site do Cursor AI", "reset_log_8": "Se os problemas persistirem, tente instalar o Cursor AI em outro local", "reset_log_9": "Se encontrar problemas, abra uma issue no Github em https://github.com/yeongpin/cursor-free-vip/issues", "unexpected_error": "Ocorreu um erro inesperado: {error}", "report_issue": "Por favor, relate este problema no Github em https://github.com/yeongpin/cursor-free-vip/issues", "keyboard_interrupt": "Processo interrompido pelo usuário. Saindo...", "return_to_main_menu": "Re<PERSON><PERSON>do ao menu principal...", "process_interrupted": "Processo interrompido. Saindo...", "press_enter_to_return_to_main_menu": "Pressione Enter para retornar ao menu principal...", "removing_known": "Removendo arquivos conhecidos de teste/licença", "performing_deep_scan": "Realizando varredura profunda por arquivos adicionais de teste/licença", "found_additional_potential_license_trial_files": "{count} arquivos adicionais de licença/teste potencialmente encontrados", "checking_for_electron_localstorage_files": "Verificando arquivos localStorage do Electron", "no_additional_license_trial_files_found_in_deep_scan": "Nenhum arquivo adicional de licença/teste encontrado na varredura profunda", "removing_electron_localstorage_files": "Removendo arquivos localStorage do Electron", "electron_localstorage_files_removed": "Arquivos localStorage do Electron removidos", "electron_localstorage_files_removal_error": "Erro ao remover arquivos localStorage do Electron: {error}", "removing_electron_localstorage_files_completed": "Remoção dos arquivos localStorage do Electron concluída", "warning_title": "AVISO", "direct_advanced_navigation": "Tentando navegação direta para guia avançada", "delete_input_error": "Erro a localização de exclusão de exclusão: {error}", "delete_input_not_found_continuing": "Exclua a entrada de confirmação não encontrada, tentando continuar de qualquer maneira", "advanced_tab_not_found": "Guia avançada não encontrada após várias tentativas", "advanced_tab_error": "Erro a guia Avançado: {<PERSON><PERSON><PERSON>}", "delete_input_not_found": "Excluir entrada de confirmação não encontrada após várias tentativas", "failed_to_delete_file": "Falha ao excluir o arquivo: {Path}", "operation_cancelled": "Operação cancelada. Sair sem fazer alterações.", "removed": "Removido: {caminho}", "warning_6": "Você precisará configurar o Cursor AI novamente após a execução desta ferramenta.", "delete_input_retry": "Excluir a entrada não encontrada, Tent {Tent}/{max_attempts}", "cursor_reset_failed": "Cursor AI Editor <PERSON><PERSON><PERSON><PERSON> falhou: {Error}", "warning_4": "Para direcionar apenas arquivos do editor de IA do cursor e mecanismos de detecção de teste.", "login_redirect_failed": "Redirecionamento de login falhou, tentando navegação direta ...", "warning_5": "Outras aplicações no seu sistema não serão afetadas.", "failed_to_delete_file_or_directory": "Falha ao excluir o arquivo ou diretório: {Path}", "failed_to_delete_directory": "Falha ao excluir o Diretório: {Path}", "resetting_cursor": "Redefinir o cursor AI Editor ... por favor, espere.", "cursor_reset_completed": "O editor do cursor AI foi totalmente redefinido e detecção de teste ignorada!", "warning_3": "Seus arquivos de código não serão afetados e a ferramenta foi projetada", "advanced_tab_retry": "<PERSON><PERSON><PERSON> n<PERSON> encontrado, Tente {Tent}/{max_attempts}", "advanced_tab_clicked": "Clicou na guia avançada", "completed_in": "<PERSON><PERSON><PERSON><PERSON><PERSON> em {time} segundos", "already_on_settings": "Já na página Configurações", "delete_button_retry": "Botão de exclusão não encontrado, tentativa {tentativa}/{max_attempts}", "found_danger_zone": "Encontrou seção de zona de perigo", "failed_to_remove": "Falha ao remover: {caminho}", "failed_to_reset_machine_guid": "Falha ao redefinir a máquina guia", "deep_scanning": "Executando a varredura profunda para obter arquivos de tentativa/licença adicionais", "delete_button_clicked": "Clicou no botão Excluir conta", "warning_7": "Use por sua conta e risco", "delete_button_not_found": "Exclua o botão da conta não encontrado após várias tentativas", "delete_button_error": "Erro a localizar o botão Excluir: {Error}", "warning_1": "Esta ação excluirá todas as configurações do cursor ai,", "warning_2": "configurações e dados em cache. Esta ação não pode ser desfeita.", "navigating_to_settings": "Navegando para configurações da página ...", "cursor_reset_cancelled": "Editor de cursor redefinido cancelado. Sair sem fazer alterações."}, "chrome_profile": {"title": "Seleção de Perfil do Chrome", "select_profile": "Selecione um perfil do Chrome para usar:", "profile_list": "<PERSON><PERSON><PERSON> dispon<PERSON>:", "default_profile": "<PERSON><PERSON><PERSON>", "profile": "Perfil {number}", "no_profiles": "Nenhum perfil do Chrome encontrado", "error_loading": "Erro ao carregar perfis do Chrome: {error}", "profile_selected": "Perfil selecionado: {profile}", "invalid_selection": "Seleção inválida. Por favor, tente novamente", "warning_chrome_close": "Aviso: <PERSON><PERSON> f<PERSON> todos os processos do Chrome em execução"}, "restore": {"title": "Restaurar ID da Máquina do Backup", "starting": "Iniciando processo de restauração de ID da máquina", "no_backups_found": "Nenhum backup encontrado", "available_backups": "Backups disponíveis", "select_backup": "Selecione um backup para restaurar", "to_cancel": "para cancelar", "operation_cancelled": "Operação cancelada", "invalid_selection": "<PERSON><PERSON><PERSON> inválida", "please_enter_number": "Por favor, insira um número válido", "missing_id": "ID ausente: {id}", "read_backup_failed": "Falha ao ler arquivo de backup: {error}", "current_file_not_found": "Arquivo de armazenamento atual não encontrado", "current_backup_created": "Backup do arquivo de armazenamento atual criado", "storage_updated": "Arquivo de armazenamento atualizado com sucesso", "update_failed": "Falha ao atualizar arquivo de armazenamento: {error}", "sqlite_not_found": "Banco de dados SQLite não encontrado", "updating_sqlite": "Atualizando banco de dados SQLite", "updating_pair": "Atualizando par chave-valor", "sqlite_updated": "Banco de dados SQLite atualizado com sucesso", "sqlite_update_failed": "Falha ao atualizar banco de dados SQLite: {error}", "machine_id_backup_created": "Backup do arquivo machineId criado", "backup_creation_failed": "Falha ao criar backup: {error}", "machine_id_updated": "Arquivo machineId atualizado com sucesso", "machine_id_update_failed": "Falha ao atualizar arquivo machineId: {error}", "updating_system_ids": "Atualizando IDs do sistema", "system_ids_update_failed": "Falha ao atualizar IDs do sistema: {error}", "permission_denied": "Permissão negada. Tente executar como administrador", "windows_machine_guid_updated": "GUID da máquina Windows atualizado com sucesso", "update_windows_machine_guid_failed": "Falha ao atualizar GUID da máquina Windows: {error}", "windows_machine_id_updated": "ID da máquina Windows atualizado com sucesso", "update_windows_machine_id_failed": "Falha ao atualizar ID da máquina Windows: {error}", "sqm_client_key_not_found": "Chave de registro SQMClient não encontrada", "update_windows_system_ids_failed": "Falha ao atualizar IDs do sistema Windows: {error}", "macos_platform_uuid_updated": "UUID da plataforma macOS atualizado com sucesso", "failed_to_execute_plutil_command": "Falha ao executar comando plutil", "update_macos_system_ids_failed": "Falha ao atualizar IDs do sistema macOS: {error}", "ids_to_restore": "IDs da máquina para restaurar", "confirm": "Tem certeza que deseja restaurar esses IDs?", "success": "ID da máquina restaurado com sucesso", "process_error": "Erro no processo de restauração: {error}", "press_enter": "Pressione Enter para continuar"}, "oauth": {"no_chrome_profiles_found": "<PERSON><PERSON> foram encontrados perfis de cromo, usando padrão", "failed_to_delete_account": "Falha ao excluir a conta: {erro}", "starting_new_authentication_process": "Iniciando novo processo de autenticação ...", "found_email": "E -mail encontrado: {email}", "github_start": "<PERSON><PERSON><PERSON> Start", "already_on_settings_page": "Já na página Configurações!", "starting_github_authentication": "Iniciando a autenticação do GitHub ...", "account_is_still_valid": "A conta ainda é válida (uso: {USAGE})", "status_check_error": "Verificação de status Erro: {erro}", "authentication_timeout": "Timeout de autenticação", "google_start": "Google Start", "using_first_available_chrome_profile": "Usando o primeiro perfil Chrome disponível: {perfil}", "no_compatible_browser_found": "Nenhum navegador compatível encontrado. Instale o Google Chrome ou Chromium.", "usage_count": "Contagem de uso: {Us<PERSON>}", "authentication_successful_getting_account_info": "Autenticação bem -sucedida, obtendo informações da conta ...", "found_chrome_at": "Encontrado Chrome em: {Path}", "error_getting_user_data_directory": "Erro obtendo o diretório de dados do usuário: {erro}", "error_finding_chrome_profile": "Erro a encontrar o perfil do Chrome, usando o padrão: {error}", "auth_update_success": "Sucesso de atualização de autenticação", "authentication_successful": "Autenticação bem -sucedida - email: {email}", "authentication_failed": "Autenticação falhou: {erro}", "warning_browser_close": "Aviso: isso fechar<PERSON> todos os processos de {navegador}", "supported_browsers": "Navegadores suportados para {plataforma}", "authentication_button_not_found": "Botão de autenticação não encontrado", "starting_new_google_authentication": "Iniciando nova autenticação do Google ...", "waiting_for_authentication": "Esperando por autenticação ...", "found_default_chrome_profile": "Perfil do Chrome padrão encontrado", "could_not_check_usage_count": "Não foi possível verificar a contagem de uso: {error}", "starting_browser": "Navegador inicial em: {Path}", "token_extraction_error": "Erro de extração de token: {error}", "profile_selection_error": "Erro durante a seleção do perfil: {error}", "warning_could_not_kill_existing_browser_processes": "Aviso: não foi possível matar processos existentes do navegador: {error}", "browser_failed_to_start": "O navegador não conseguiu iniciar: {Error}", "starting_re_authentication_process": "Iniciando o processo de re-autenticação ...", "redirecting_to_authenticator_cursor_sh": "Redirecionando para autenticator.cursor.sh ...", "found_browser_data_directory": "Diretório de dados do navegador encontrado: {caminho}", "browser_not_found_trying_chrome": "Não foi possível encontrar {navegador}, tentando o Chrome em vez", "found_cookies": "Encontrado {count} cookies", "auth_update_failed": "Atualização de autenticação falhou", "browser_failed_to_start_fallback": "O navegador não conseguiu iniciar: {Error}", "failed_to_delete_expired_account": "Falha ao excluir a conta expirada", "navigating_to_authentication_page": "Navegando para a página de autenticação ...", "browser_closed": "<PERSON><PERSON><PERSON><PERSON> fechado", "initializing_browser_setup": "Inicializando a configuração do navegador ...", "failed_to_delete_account_or_re_authenticate": "Falha ao excluir a conta ou re-autenticar: {Error}", "detected_platform": "Plataforma detectada: {plataforma}", "failed_to_extract_auth_info": "Falha ao extrair informações de autenticação: {error}", "starting_google_authentication": "Iniciando a autenticação do Google ...", "browser_failed": "O navegador não conseguiu iniciar: {Error}", "using_browser_profile": "Usando o perfil do navegador: {perfil}", "consider_running_without_sudo": "Considere executar o script sem sudo", "try_running_without_sudo_admin": "Tente correr sem privilégios de sudo/administrador", "page_changed_checking_auth": "Página alterada, verificando a autenticação ...", "running_as_root_warning": "Correr como root não é recomendado para automação do navegador", "please_select_your_google_account_to_continue": "Selecione sua conta do Google para continuar ...", "browser_setup_failed": "Falha na configuração do navegador: {error}", "missing_authentication_data": "Dados de autenticação ausentes: {dados}", "using_configured_browser_path": "Usando o caminho configurado {navegador}: {caminho}", "killing_browser_processes": "Matar {navegador} processos ...", "could_not_find_usage_count": "Não foi possível encontrar contagem de uso: {error}", "account_has_reached_maximum_usage": "A conta atingiu o máximo de uso, {excluindo}", "browser_setup_completed": "A configuração do navegador concluiu com êxito", "could_not_find_email": "Não foi possível encontrar email: {error}", "found_browser_user_data_dir": "Encontrado {navegador} diretório de dados do usuário: {caminho}", "user_data_dir_not_found": "{navegador} diretório de dados do usuário não encontrado em {path}, tentará o Chrome em vez", "invalid_authentication_type": "Tipo de autenticação inválido"}, "auth_check": {"token_length": "Comprimento do token: {comprimento} caracteres", "usage_response_status": "Estado de resposta de uso: {resposta}", "operation_cancelled": "Operação cancelada pelo usuário", "error_getting_token_from_db": "Erro de obter token do banco de dados: {error}", "checking_usage_information": "Verificando informações de uso ...", "usage_response": "Resposta de uso: {resposta}", "authorization_failed": "Autorização falhou!", "authorization_successful": "Autorização bem -sucedida!", "request_timeout": "Solicitação cronometrada", "check_error": "Autorização de verificação de erro: {error}", "connection_error": "<PERSON>rro <PERSON>", "invalid_token": "To<PERSON> in<PERSON>lid<PERSON>", "check_usage_response": "Verifique a resposta de uso: {resposta}", "enter_token": "Digite o seu token do cursor:", "token_found_in_db": "Token encontrado no banco de dados", "user_unauthorized": "O usuário não é autorizado", "checking_authorization": "Verificando a autorização ...", "error_generating_checksum": "Erro de geração de soma de verificação: {error}", "unexpected_error": "<PERSON><PERSON> inesperado: {erro}", "token_source": "Obter token do banco de dados ou entrada manualmente? (d/m, padrão: D)", "user_authorized": "O usuário está autorizado", "token_not_found_in_db": "Token não encontrado no banco de dados", "unexpected_status_code": "Código de status inesperado: {code}", "jwt_token_warning": "O token parece estar no formato JWT, mas a verificação da API retornou um código de status inesperado. O token pode ser válido, mas o acesso da API é restrito.", "getting_token_from_db": "Obtendo token do banco de dados ...", "cursor_acc_info_not_found": "cursor_acc_info.py não encontrado"}, "account_delete": {"delete_input_not_found": "Excluir entrada de confirmação não encontrada após várias tentativas", "confirm_button_not_found": "Confirme o botão não encontrado após várias tentativas", "logging_in": "Faça login com o Google ...", "confirm_button_error": "Erro para encontrar o botão Confirmar: {Error}", "delete_button_clicked": "Clicou no botão Excluir conta", "confirm_prompt": "Tem certeza de que deseja prosseguir? (S/N):", "delete_button_error": "Erro a localizar o botão Excluir: {Error}", "interrupted": "Processo de exclusão de conta interrompido pelo usuário.", "cancelled": "Exclusão de conta cancelada.", "error": "<PERSON>rro durante a exclusão da conta: {erro}", "delete_input_not_found_continuing": "Exclua a entrada de confirmação não encontrada, tentando continuar de qualquer maneira", "advanced_tab_retry": "<PERSON><PERSON><PERSON> n<PERSON> encontrado, Tente {Tent}/{max_attempts}", "waiting_for_auth": "Esperando pela autenticação do Google ...", "typed_delete": "\"Excluir\" digitado na caixa de confirmação", "trying_settings": "<PERSON><PERSON><PERSON> nave<PERSON> para a página de configurações ...", "delete_input_retry": "Excluir a entrada não encontrada, Tent {Tent}/{max_attempts}", "email_not_found": "Email não encontrado: {erro}", "delete_button_not_found": "Exclua o botão da conta não encontrado após várias tentativas", "already_on_settings": "Já na página Configurações", "failed": "O processo de exclusão da conta falhou ou foi cancelado.", "warning": "Aviso: isso excluirá permanentemente sua conta do cursor. Esta ação não pode ser desfeita.", "direct_advanced_navigation": "Tentando navegação direta para guia avançada", "advanced_tab_not_found": "Guia avançada não encontrada após várias tentativas", "auth_timeout": "Tempo limite de autenticação, continuando de qualquer maneira ...", "select_google_account": "Selecione sua conta do Google ...", "google_button_not_found": "Botão de login do google não encontrado", "found_danger_zone": "Encontrou seção de zona de perigo", "account_deleted": "Conta excluída com sucesso!", "starting_process": "Processo de exclusão de conta inicial ...", "advanced_tab_error": "Erro a guia Avançado: {<PERSON><PERSON><PERSON>}", "delete_button_retry": "Botão de exclusão não encontrado, tentativa {tentativa}/{max_attempts}", "login_redirect_failed": "Redirecionamento de login falhou, tentando navegação direta ...", "unexpected_error": "<PERSON><PERSON> inesperado: {erro}", "delete_input_error": "Erro a localização de exclusão de exclusão: {error}", "login_successful": "<PERSON>gin bem -sucedido", "advanced_tab_clicked": "Clicou na guia avançada", "unexpected_page": "Página inesperada após login: {url}", "found_email": "E -mail encontrado: {email}", "title": "Ferramenta de exclusão de conta do cursor Google", "navigating_to_settings": "Navegando para configurações da página ...", "success": "Sua conta do cursor foi excluída com sucesso!", "confirm_button_retry": "Confirme o botão não encontrado, Tent {Tent}/{max_attempts}"}, "manual_auth": {"auth_type_selected": "Tipo de autenticação selecionada: {type}", "proceed_prompt": "Prosseguir? (S/N):", "auth_type_github": "<PERSON><PERSON><PERSON>", "confirm_prompt": "Confirme as seguintes informações:", "invalid_token": "Token inválido. Autenticação abortada.", "continue_anyway": "Continuar de qualquer maneira? (S/N):", "token_verified": "Token Verificado com sucesso!", "error": "Erro: {erro}", "auth_update_failed": "Falha ao atualizar informações de autenticação", "auth_type_prompt": "Selecione Tipo de autenticação:", "auth_type_auth0": "Auth_0 (pad<PERSON><PERSON>)", "verifying_token": "Verificando a validade do token ...", "auth_updated_successfully": "Informações de autenticação são atualizadas com sucesso!", "email_prompt": "Digite email (deixe em branco para o email aleatório):", "token_prompt": "Digite seu token cursor (Access_Token/Refresh_Token):", "title": "Autenticação do cursor manual", "token_verification_skipped": "Verificação do token Saltada (check_user_authorized.py não encontrado)", "random_email_generated": "Email aleatório gera<PERSON>: {email}", "auth_type_google": "Google", "token_required": "É necessário token", "operation_cancelled": "Operação cancelada", "token_verification_error": "Erro verificando o token: {error}", "updating_database": "Atualizando o banco de dados de autenticação do cursor ..."}, "token": {"refreshing": "Token refrescante ...", "extraction_error": "Erro extraindo o token: {error}", "invalid_response": "Resposta JSON inválida do servidor Refresh", "no_access_token": "Sem token de acesso em resposta", "connection_error": "Erro de conexão para atualizar o servidor", "unexpected_error": "<PERSON><PERSON> inesperado durante a atualização do token: {error}", "server_error": "Atualizar erro do servidor: http {status}", "refresh_success": "Token atualizado com sucesso! Válido para {dias} dias (expira: {expire})", "request_timeout": "Solicitação para atualizar o servidor cronometrado", "refresh_failed": "A atualização do token falhou: {erro}"}, "browser_profile": {"profile_selected": "Perfil selecionado: {perfil}", "default_profile": "<PERSON><PERSON><PERSON>", "no_profiles": "<PERSON>ão há perfis {navegador} encontrados", "select_profile": "Se<PERSON>cione {navegador} perfil para usar:", "error_loading": "<PERSON>rro de carregamento {navegador} perfis: {error}", "invalid_selection": "Se<PERSON>ção inválida. Por favor, tente novamente.", "title": "Seleção de perfil do navegador", "profile": "Perfil {número}", "profile_list": "Disponível {navegador} perfis:"}, "github_register": {"feature2": "Registra uma nova conta do GitHub com credenciais aleatórias.", "feature6": "<PERSON><PERSON> to<PERSON> as credenciais em um arquivo.", "starting_automation": "Automação inicial ...", "feature1": "Gera um email temporário usando 1Secmail.", "title": "Github + Cursor AI Automação de registro", "github_username": "Nome de usuário do Github", "check_browser_windows_for_manual_intervention_or_try_again_later": "<PERSON><PERSON><PERSON><PERSON> as jane<PERSON> do navegador para intervenção manual ou tente novamente mais tarde.", "warning1": "Este script automatiza a criação de contas, que pode violar os Termos de Serviço Github/Cursor.", "feature4": "Faça login no cursor IA usando a autenticação do GitHub.", "invalid_choice": "Escolha inválida. Por <PERSON>, digite 'sim' ou 'não'", "completed_successfully": "Github + Registro do cursor concluído com êxito!", "warning2": "Requer acesso à Internet e privilégios administrativos.", "registration_encountered_issues": "O registro do Github + Cursor encontrou problemas.", "credentials_saved": "<PERSON><PERSON><PERSON> credenciais foram salvas para github_cursor_accounts.txt", "feature3": "Verifica o email do GitHub automaticamente.", "github_password": "Senha do github", "features_header": "Características", "feature5": "Redefina o ID da máquina para ignorar a detecção de teste.", "warning4": "Use com responsabilidade e por sua conta e risco.", "warning3": "CAPTCHA ou verificação adicional podem interromper a automação.", "cancelled": "Operação cancelada", "warnings_header": "Avisos", "program_terminated": "Programa encerrado pelo usuário", "confirm": "Tem certeza de que deseja prosseguir?", "email_address": "Endereço de email"}, "account_info": {"subscription": "Subscrição", "failed_to_get_account_info": "Falha ao obter informações da conta", "subscription_type": "Tipo de assinatura", "pro": "<PERSON><PERSON><PERSON>", "failed_to_get_account": "Falha ao obter informações da conta", "config_not_found": "Configuração não encontrada.", "premium_usage": "Uso premium", "failed_to_get_subscription": "Falha ao obter informações de assinatura", "basic_usage": "Uso básico", "premium": "Premium", "free": "Livre", "email_not_found": "E -mail não encontrado", "title": "Informações da conta", "inactive": "Inativo", "remaining_trial": "Teste restante", "enterprise": "Empresa", "lifetime_access_enabled": "Acesso ao longo da vida ativado", "failed_to_get_usage": "Falha ao obter informações de uso", "usage_not_found": "Uso não encontrado", "days_remaining": "<PERSON>as restantes", "failed_to_get_token": "Falhou em obter token", "token": "Token", "subscription_not_found": "Informações de assinatura não encontradas", "days": "dias", "team": "Equipe", "token_not_found": "Token não encontrado", "pro_trial": "Trial Pro", "email": "E-mail", "active": "Ativo", "failed_to_get_email": "Falha ao obter endereço de e -mail", "trial_remaining": "Trial profissional restante", "usage": "<PERSON><PERSON>"}, "config": {"configuration": "Configuração", "config_updated": "Config <PERSON><PERSON><PERSON><PERSON>", "file_owner": "Proprietário do arquivo: {proprietário}", "error_checking_linux_paths": "Erro verificando os caminhos do Linux: {Error}", "storage_file_is_empty": "O arquivo de armazenamento está vazio: {storage_path}", "config_directory": "Diretório de configuração", "documents_path_not_found": "Documentos Caminho não encontrado, usando o diretório atual", "config_not_available": "Configuração não disponível", "please_make_sure_cursor_is_installed_and_has_been_run_at_least_once": "Certifique -se de que o cursor esteja instalado e foi executado pelo menos uma vez", "neither_cursor_nor_cursor_directory_found": "Nem o cursor nem o diretório cursor encontrados em {config_base}", "config_created": "Config criado: {config_file}", "using_temp_dir": "Usando o diretório temporário devido a erro: {path} (erro: {error})", "storage_file_not_found": "Arquivo de armazenamento não encontrado: {storage_path}", "the_file_might_be_corrupted_please_reinstall_cursor": "O arquivo pode estar corrompido, reinstale o cursor", "error_getting_file_stats": "Erro obtendo estatísticas de arquivo: {error}", "enabled": "Habilitado", "backup_created": "Backup criado: {Path}", "file_permissions": "Permissões de arquivo: {permissões}", "config_setup_error": "Erro Configuração de configuração: {error}", "config_force_update_enabled": "Atualização de força de arquivo de configuração ativada, executando atualização forçada", "config_removed": "Arquivo de configuração removido para atualização forçada", "file_size": "Tamanho do arquivo: {size} bytes", "error_reading_storage_file": "Erro ao ler Arquivo de armazenamento: {erro}", "config_force_update_disabled": "Atualização de força de arquivo de configuração desativada, pulando a atualização forçada", "config_dir_created": "Diretório de configuração criado: {caminho}", "config_option_added": "Opção de configuração adicionada: {option}", "file_group": "Grupo de arquivos: {grupo}", "and": "E", "backup_failed": "Falha ao fazer backup de configuração: {error}", "force_update_failed": "Falha na atualização de força: {error}", "also_checked": "Também verificado {caminho}", "storage_directory_not_found": "Diretório de armazenamento não encontrado: {storage_dir}", "storage_file_found": "Arquivo de armazenamento encontrado: {storage_path}", "try_running": "<PERSON><PERSON> correr: {comando}", "disabled": "Desabilitado", "storage_file_is_valid_and_contains_data": "O arquivo de armazenamento é válido e contém dados", "permission_denied": "Permissão negada: {storage_path}"}, "bypass": {"found_product_json": "Found Product.json: {Path}", "starting": "Iniciando a versão do cursor ...", "version_updated": "Versão atualizada de {Old} para {new}", "menu_option": "Verificação da versão do cursor de desvio", "unsupported_os": "Sistema operacional não suportado: {System}", "backup_created": "Backup criado: {Path}", "current_version": "Versão atual: {vers<PERSON>}", "no_write_permission": "Nenhuma permissão de gravação para arquivo: {caminho}", "localappdata_not_found": "Variável de ambiente localAppData não encontrada", "write_failed": "Falha ao escrever o produto.json: {error}", "description": "Esta ferramenta modifica o produto do cursor.json para ignorar as restrições da versão", "bypass_failed": "Versão Bypass falhou: {error}", "title": "Ferramenta de desvio da versão cursor", "no_update_needed": "Nenhuma atualização necessária. Versão atual {versão} já está> = 0,46.0", "read_failed": "Falha ao ler o produto.json: {error}", "stack_trace": "Rastreamento da pilha", "product_json_not_found": "Product.json não encontrado em caminhos comuns do Linux", "file_not_found": "Arquivo não encontrado: {caminho}"}, "bypass_token_limit": {"description": "Esta ferramenta modifica o arquivo workbench.desktop.main.js para ignorar o limite do token", "press_enter": "Pressione Enter para continuar ...", "title": "Ipassue Token Limit Tool"}, "tempmail": {"config_error": "Erro de arquivo de configuração: {erro}", "general_error": "Ocorreu um erro: {erro}", "no_email": "Nenhum e -mail de verificação do cursor encontrado", "extract_code_failed": "Código de verificação de extração falhou: {erro}", "checking_email": "Verificando o e -mail de verificação do cursor ...", "configured_email": "Email configurado: {email}", "no_code": "Não foi possível obter o código de verificação", "check_email_failed": "Verifique o e -mail falhado: {erro}", "verification_code": "Código de verificação: {code}", "email_found": "E -mail de verificação do cursor encontrado"}}