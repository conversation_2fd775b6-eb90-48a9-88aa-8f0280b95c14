{"menu": {"title": "Възможни избори", "exit": "Затвори програмата", "reset": "Нулирай ид-то на компютъра", "register": "Регистр<PERSON><PERSON><PERSON><PERSON> нов Курсор акаунт", "register_google": "Регистрирай се с Google акаунт", "register_github": "Регистрирай се с GitHub акаунт", "register_manual": "Регистрирай се със свой имейл по избор", "quit": "Затвори приложението Курсор", "select_language": "Избери език", "es": "Spanish", "input_choice": "Моля, въведете своя избор ({choices})", "invalid_choice": "Невалиден избор. Моля, въведете избор от {choices}.", "program_terminated": "Програмата беше затворена от вас", "error_occurred": "Възникна грешка: {error}. Опитайте отново", "press_enter": "Натисне<PERSON>е Enter, за да излезете", "disable_auto_update": "Спрете автоматичните ъпдейти на Курсор", "lifetime_access_enabled": "ДОЖИВОТЕН ДОСТЪП Е АКТИВИРАН", "totally_reset": "Нулирайте изцяло Курсор", "outdate": "Изтекъл срок", "temp_github_register": "Временно регистриране с GitHub", "coming_soon": "Очаквайте скоро", "fixed_soon": "Ще бъде поправено скоро", "contribute": "Принос към проекта", "config": "Покажи конфигурацията", "delete_google_account": "Изтрий Google акаунта на Cursor", "continue_prompt": "Продължи? (y/N): ", "operation_cancelled_by_user": "Операцията е отменена от потребителя", "exiting": "Излизане ......", "bypass_version_check": "Пропусни проверката на версията на Cursor", "check_user_authorized": "Провери оторизацията на потребителя", "select_chrome_profile": "Избери Chrome профил", "bypass_token_limit": "Заобикаляне на ограничението на токените", "restore_machine_id": "Възстановяване на машинен идентификатор от резервно копие", "lang_invalid_choice": "Невалиден избор. Моля, въведете една от следните опции: ({lang_choices})", "admin_required": "Изпълнявайки се като изпълними, администраторските привилегии са необходими.", "language_config_saved": "Езиковата конфигурация се запази успешно", "admin_required_continue": "Продължаване без привилегии на администратор.", "manual_custom_auth": "Ръчен персонал<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "languages": {"ar": "Арабски", "en": "English", "zh_cn": "简体中文", "zh_tw": "繁體中文", "vi": "Vietnamese", "nl": "Dutch", "de": "German", "fr": "French", "pt": "Portuguese", "ru": "Russian", "tr": "Turkish", "bg": "Bulgarian", "es": "Spanish", "it": "Италиански", "ja": "Японски"}, "quit_cursor": {"start": "Започни излизането от Курсор", "no_process": "Няма съществуващ процес, засяга<PERSON> Курсор", "terminating": "Прекратяване на процес {pid}", "waiting": "Изчакване на процеса да приключи", "success": "Всички процеси, свързани с Курсор, бяха прекратени", "timeout": "Таймаут на процеса: {pids}", "error": "Възникна грешка: {error}"}, "reset": {"title": "", "checking": "Проверка на конфигурационния файл", "not_found": "Конфигурационният файл не беше намерен", "no_permission": "Конфигурационният файл не може да бъде прочетен или записан. Моля, проверете разрешенията на файла", "reading": "Четене на текущата конфигурация", "creating_backup": "Създаване на резервно копие на конфигурацията", "backup_exists": "Резервното копие вече съществува. Стъпката за резервно копие се пропуска", "generating": "Генериране на ново машинно ID", "saving_json": "Запазване на новата конфигурация в JSON", "success": "Машинното ID беше успешно нулирано", "new_id": "Ново машинно ID", "permission_error": "Грешка в разрешенията: {error}", "run_as_admin": "Моля, опитайте да стартирате тази програма като администратор", "process_error": "Грешка при нулиране: {error}", "updating_sqlite": "Актуализиране на SQLite базата данни", "updating_pair": "Актуализиране на двойката ключ-стойност", "sqlite_success": "SQLite базата данни беше успешно актуализирана", "sqlite_error": "Грешка при актуализиране на SQLite базата данни: {error}", "press_enter": "Натисне<PERSON>е Enter, за да излезете", "unsupported_os": "Неподдържана операционна система: {os}", "linux_path_not_found": "Linux пътят не беше намерен", "updating_system_ids": "Актуализиране на системните ID", "system_ids_updated": "Системните ID бяха успешно актуализирани", "system_ids_update_failed": "Грешка при актуализиране на системните ID: {error}", "windows_guid_updated": "Windows GUID беше успешно актуализиран", "windows_permission_denied": "Достъпът до Windows беше отказан", "windows_guid_update_failed": "Грешка при актуализиране на Windows GUID", "macos_uuid_updated": "macOS UUID беше успешно актуализиран", "plutil_command_failed": "Командата plutil не беше успешна", "start_patching": "Започване на прилагане на корекция за getMachineId", "macos_uuid_update_failed": "Грешка при актуализиране на macOS UUID", "current_version": "Текуща версия на Курсор: {version}", "patch_completed": "Корекцията на getMachineId беше успешно завършена", "patch_failed": "Грешка при прилагане на корекция за getMachineId: {error}", "version_check_passed": "Проверката на версията на Курсор беше успешна", "file_modified": "Файлът беше променен", "version_less_than_0_45": "Версията на Курсор е < 0.45.0, корекцията на getMachineId се пропуска", "detecting_version": "Откриване на версията на Курсор", "patching_getmachineid": "Прилагане на корекция за getMachineId", "version_greater_than_0_45": "Версията на Курсор е >= 0.45.0, прилага се корекция за getMachineId", "permission_denied": "Достъпът беше отказан: {error}", "backup_created": "Резервното копие беше създадено", "update_success": "Актуализацията беше успешна", "update_failed": "Грешка при актуализация: {error}", "windows_machine_guid_updated": "Windows машинното GUID беше успешно актуализирано", "reading_package_json": "Четене на package.json {path}", "invalid_json_object": "Невалиден JSON обект", "no_version_field": "Няма поле за версия в package.json", "version_field_empty": "Полето за версия е празно", "invalid_version_format": "Невалиден формат на версията: {version}", "found_version": "Намерена версия: {version}", "version_parse_error": "Грешка при анализ на версията: {error}", "package_not_found": "Package.json не беше намерен: {path}", "check_version_failed": "Грешка при проверка на версията: {error}", "stack_trace": "Проследяване на стека", "version_too_low": "Версията на Курсор е твърде ниска: {version} < 0.45.0", "windows_machine_id_updated": "Успешно актуализиран ID на машината на Windows актуализиран", "modify_file_failed": "Променете файла не е успешен: {Грешка}", "file_not_found": "Файлът не е намерен: {path}", "update_windows_machine_guid_failed": "Актуализир<PERSON>йте Windows Machine Guid неуспешно: {Грешка}", "update_windows_machine_id_failed": "Актуализиране на ID на Windows Machine не успя: {грешка}", "path_not_found": "Пътят не е намерен: {path}", "no_write_permission": "Без разрешение за запис: {path}"}, "register": {"title": "Инструмент за регистрация в Курсор", "start": "Започване на процеса на регистрация...", "handling_turnstile": "Обработка на проверка за сигурност...", "retry_verification": "Опит за повторна проверка...", "detect_turnstile": "Проверка на защитната врата...", "verification_success": "Проверката за сигурност беше успешна", "starting_browser": "Стартиране на браузъра...", "form_success": "Формата беше успешно изпратена", "browser_started": "Браузърът беше успешно стартиран", "waiting_for_second_verification": "Изчакване на втората проверка по имейл...", "waiting_for_verification_code": "Изчакване на код за потвърждение...", "password_success": "Паролата беше успешно зададена", "password_error": "Грешка при задаване на парола: {error}. Моля, опитайте отново", "waiting_for_page_load": "Зареждане на страницата...", "first_verification_passed": "Първата проверка беше успешна", "mailbox": "Успешен достъп до пощенската кутия", "register_start": "Начало на регистрацията", "form_submitted": "Формата беше изпратена, започване на проверка...", "filling_form": "Попълване на формуляра", "visiting_url": "Посещение на URL", "basic_info": "Основните данни бяха изпратени", "handle_turnstile": "Обработка на защитната врата", "no_turnstile": "Няма защитна врата", "turnstile_passed": "Защитната врата беше премината", "verification_start": "Започване на получаване на код за потвърждение", "verification_timeout": "Времето за получаване на код за потвърждение изтече", "verification_not_found": "Кодът за потвърждение не беше намерен", "try_get_code": "Опит | {attempt} Получаване на код за потвърждение | Оставащо време: {time}s", "get_account": "Получаване на информация за акаунта", "get_token": "Получаване на токен за сесия на Курсор", "token_success": "Токенът беше успешно получен", "token_attempt": "Опит | {attempt} опита за получаване на токен | Ще бъде опит отново след {time}s", "token_max_attempts": "Достигнат максимален брой опити ({max}) | Неуспешно получаване на токен", "token_failed": "Грешка при получаване на токен: {error}", "account_error": "Грешка при получаване на информация за акаунта: {error}", "press_enter": "Натисне<PERSON>е Enter, за да излезете", "browser_start": "Стартиране на браузъра", "open_mailbox": "Отваряне на страницата на пощенската кутия", "email_error": "Грешка при получаване на имейл адрес", "setup_error": "Грешка при настройка на имейл: {error}", "start_getting_verification_code": "Започване на получаване на код за потвърждение, ще бъде опит след 60 секунди", "get_verification_code_timeout": "Времето за получаване на код за потвърждение изтече", "get_verification_code_success": "Кодът за потвърждение беше успешно получен", "try_get_verification_code": "Опит | {attempt} Получаване на код за потвърждение | Оставащо време: {remaining_time}s", "verification_code_filled": "Кодът за потвърждение беше попълнен", "login_success_and_jump_to_settings_page": "Успешен вход и преход към страницата с настройки", "detect_login_page": "Открита е страница за вход, започване на влизане...", "cursor_registration_completed": "Регистрацията в Курсор беше завършена!", "set_password": "Задаване на парола", "basic_info_submitted": "Основните данни бяха изпратени", "cursor_auth_info_updated": "Информацията за удостоверяване на Курсор беше актуализирана", "cursor_auth_info_update_failed": "Грешка при актуализиране на информацията за удостоверяване на Курсор", "reset_machine_id": "Нулиране на машинното ID", "account_info_saved": "Информацията за акаунта беше запазена", "save_account_info_failed": "Грешка при запазване на информацията за акаунта", "get_email_address": "Получаване на имейл адрес", "update_cursor_auth_info": "Актуализиране на информацията за удостоверяване на Курсор", "register_process_error": "Грешка в процеса на регистрация: {error}", "setting_password": "Задаване на парола", "manual_code_input": "Ръчно въвеждане на код", "manual_email_input": "Ръчно въвеждане на имейл", "password": "Парола", "first_name": "Име", "last_name": "Фамилия", "exit_signal": "Сигнал за изход", "email_address": "<PERSON><PERSON><PERSON><PERSON><PERSON> адрес", "config_created": "Конфигурацията беше създадена", "verification_failed": "Проверката беше неуспешна", "verification_error": "Грешка при проверка: {error}", "config_option_added": "Добавена е опция за конфигурация: {option}", "config_updated": "Конфигурацията беше актуализирана", "password_submitted": "Паролата беше изпратена", "total_usage": "Общо използване: {usage}", "setting_on_password": "Задаване на парола", "getting_code": "Получаване на код за потвърждение, ще бъде опит след 60 секунди", "human_verify_error": "Не може да се потвърди, че потребителят е човек. Опитва се отново...", "max_retries_reached": "Достигнат максимален брой опити. Регистрацията беше неуспешна.", "using_browser": "Използване на браузър} браузър: {path}", "tracking_processes": "Проследяване {count} {браузър} процеси", "using_browser_profile": "Използване на {браузър} профил от: {user_data_dir}", "could_not_track_processes": "Не можах да проследяваме {браузър} процеси: {грешка}", "browser_path_invalid": "{браузър} Пътят е невалиден, използвайки пътя по подразбиране", "try_install_browser": "Опитайте да инсталирате браузъра с вашия мениджър на пакети", "no_new_processes_detected": "Няма нови {браузър} процеси, открити за проследяване", "make_sure_browser_is_properly_installed": "Уверете се, че {браузърът} е инсталиран правилно", "tempmail_plus_verification_completed": "Проверката tempmailplus приключи успешно", "tempmail_plus_initialized": "TempMailplus инициализира успешно", "tempmail_plus_epin_missing": "Tempmailplus epin не е конфигуриран", "using_tempmail_plus": "Използване на TempMailPlus за проверка по имейл", "tempmail_plus_verification_failed": "Проверката на tempmailplus не бе успешна: {грешка}", "tempmail_plus_disabled": "Tempmailplus е деактивиран", "tempmail_plus_email_missing": "Tempmailplus имейл не е конфигуриран", "tempmail_plus_verification_started": "Стартиране на процеса на проверка на TempMailplus", "tempmail_plus_enabled": "Tempmailplus е активиран", "tempmail_plus_config_missing": "Липсва конфигурацията TempMailplus", "tempmail_plus_init_failed": "Неуспешно инициализиране на tempmailplus: {грешка}"}, "auth": {"title": "Управление на удостоверяването на Курсор", "checking_auth": "Проверка на файла за удостоверяване", "auth_not_found": "Файлът за удостоверяване не беше намерен", "auth_file_error": "Грешка във файла за удостоверяване: {error}", "reading_auth": "Четене на файла за удостоверяване", "updating_auth": "Актуализиране на информацията за удостоверяване", "auth_updated": "Информацията за удостоверяване беше успешно актуализирана", "auth_update_failed": "Грешка при актуализиране на информацията за удостоверяване: {error}", "auth_file_created": "Файлът за удостоверяване беше създаден", "auth_file_create_failed": "Грешка при създаване на файла за удостоверяване: {error}", "press_enter": "Натисне<PERSON>е Enter, за да излезете", "reset_machine_id": "Нулиране на машинното ID", "database_connection_closed": "Връзката с базата беше затворена", "database_updated_successfully": "Базата данни беше успешно актуализирана", "connected_to_database": "Успешно свързване с базата данни", "updating_pair": "Актуализиране на двойката ключ-стойност", "db_not_found": "Файлът на базата данни не беше намерен: {path}", "db_permission_error": "Няма достъп до файла на базата данни. Моля, проверете разрешенията", "db_connection_error": "Грешка при свързване с базата данни: {error}"}, "control": {"generate_email": "Създаване на нов имейл", "blocked_domain": "Блокиран домейн", "select_domain": "Избиране на случаен домейн", "copy_email": "Копиране на имейл адрес", "enter_mailbox": "Влизане в пощенската кутия", "refresh_mailbox": "Опресняване на пощенската кутия", "check_verification": "Проверка на кода за потвърждение", "verification_found": "Кодът за потвърждение беше намерен", "verification_not_found": "Кодът за потвърждение не беше намерен", "browser_error": "Грешка при контрол на браузъра: {error}", "navigation_error": "Грешка при навигация: {error}", "email_copy_error": "Грешка при копиране на имейл: {error}", "mailbox_error": "Грешка в пощенската кутия: {error}", "token_saved_to_file": "Токенът беше запазен във файла cursor_tokens.txt", "navigate_to": "Навигира<PERSON><PERSON> към {url}", "generate_email_success": "Имейлът беше успешно създаден", "select_email_domain": "Избор на домейн за имейл", "select_email_domain_success": "Успешен избор на домейн за имейл", "get_email_name": "Получаване на име на имейл", "get_email_name_success": "Успешно получаване на име на имейл", "get_email_address": "Получаване на имейл адрес", "get_email_address_success": "Успешно получаване на имейл адрес", "enter_mailbox_success": "Успешно влизане в пощенската кутия", "found_verification_code": "Кодът за потвърждение беше намерен", "get_cursor_session_token": "Получаване на токен за сесия на Курсор", "get_cursor_session_token_success": "Успешно получаване на токен за сесия на Курсор", "get_cursor_session_token_failed": "Грешка при получаване на токен за сесия на Курсор", "save_token_failed": "Грешка при запазване на токен", "database_updated_successfully": "Базата данни беше успешно актуализирана", "database_connection_closed": "Връзката с базата данни беше затворена", "no_valid_verification_code": "Няма валиден код за потвърждение"}, "email": {"starting_browser": "Стартиране на браузъра", "visiting_site": "Посещение на сайтове за имейл домейни", "create_success": "Имейлът беше успешно създаден", "create_failed": "Грешка при създаване на имейл", "create_error": "Грешка при създаване на имейл: {error}", "refreshing": "Опресняване на имейл", "refresh_success": "Имейлът беше успешно опреснен", "refresh_error": "Грешка при опресняване на имейл: {error}", "refresh_button_not_found": "Бутонът за опресняване не беше намерен", "verification_found": "Потвърждението беше намерено", "verification_not_found": "Потвърждението не беше намерено", "verification_error": "Грешка при потвърждение: {error}", "verification_code_found": "Кодът за потвърждение беше намерен", "verification_code_not_found": "Кодът за потвърждение не беше намерен", "verification_code_error": "Грешка при код за потвърждение: {error}", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON> адрес", "all_domains_blocked": "Всички домейни са блокирани, смяна на услуга", "no_available_domains_after_filtering": "Няма налични домейни след филтриране", "switching_service": "Смяна на услугата {service}", "domains_list_error": "Грешка при получаване на списък с домейни: {error}", "failed_to_get_available_domains": "Неуспешно получаване на налични домейни", "domains_excluded": "Изключени домейни: {domains}", "failed_to_create_account": "Неуспешно създаване на акаунт", "account_creation_error": "Грешка при създаване на акаунт: {error}", "blocked_domains": "Блокирани домейни: {domains}", "blocked_domains_loaded": "Блокирани домейни са заредени: {count}", "blocked_domains_loaded_error": "Грешка при зареждане на блокирани домейни: {error}", "blocked_domains_loaded_success": "Блокираните домейни бяха успешно заредени", "blocked_domains_loaded_timeout": "Време за зареждане на блокирани домейни: {timeout}s", "blocked_domains_loaded_timeout_error": "Грешка при време за зареждане на блокирани домейни: {error}", "available_domains_loaded": "Налични домейни са заредени: {count}", "domains_filtered": "Филтрирани домейни: {count}", "trying_to_create_email": "Опит за създаване на имейл: {email}", "domain_blocked": "Домейнът е блокиран: {domain}", "no_display_found": "Не е намерен дисплей. Уверете се, че X сървърът работи.", "try_install_chromium": "Опитайте: <PERSON><PERSON> Инсталирайте хром-браузър", "extension_load_error": "Грешка в натоварването на удължаване: {Грешка}", "try_export_display": "Опитайте: Експортиране на дисплея =: 0", "make_sure_chrome_chromium_is_properly_installed": "Уверете се, че хромът/хромът е правилно инсталиран", "using_chrome_profile": "Използване на Chrome профил от: {user_data_dir}"}, "update": {"title": "Деактивиране на автоматичните актуализации на Курсор", "disable_success": "Автоматичните актуализации бяха успешно деактивирани", "disable_failed": "Грешка при деактивиране на автоматичните актуализации: {error}", "press_enter": "Натисне<PERSON>е Enter, за да излезете", "start_disable": "Започване на деактивиране на автоматичните актуализации", "killing_processes": "Прекратяване на процеси", "processes_killed": "Процесите бяха прекратени", "removing_directory": "Премахване на директория", "directory_removed": "Директорията беше премахната", "creating_block_file": "Създаване на блокиращ файл", "block_file_created": "Блокиращият файл беше създаден", "block_file_locked_error": "Заключена грешка в блок файл: {Грешка}", "clear_update_yml_failed": "Неуспешно изчистване на Update.yml файл: {грешка}", "yml_already_locked_error": "Update.yml файл вече заключена грешка: {грешка}", "yml_locked": "Update.yml файл е заключен", "yml_locked_error": "Update.yml Заключена файл Грешка: {Грешка}", "block_file_already_locked": "Блок файлът вече е заключен", "directory_locked": "Директорията е заключена: {path}", "create_block_file_failed": "Неуспешно създаване на блок файл: {грешка}", "block_file_locked": "блок файл е заключен", "update_yml_not_found": "Update.yml файл не е намерен", "remove_directory_failed": "Неуспешно премахване на директорията: {грешка}", "block_file_already_locked_error": "Блок файл вече заключена грешка: {грешка}", "update_yml_cleared": "Update.yml файл, изчистен", "yml_already_locked": "Update.yml файл вече е заключен", "unsupported_os": "Неподдържана ОС: {Система}", "clearing_update_yml": "Изчистване на актуализация.yml файл"}, "updater": {"checking": "Проверка за актуализации...", "new_version_available": "Налична е нова версия! (Текуща: {current}, Последна: {latest})", "updating": "Актуализиране до последната версия. Програмата ще се рестартира автоматично.", "up_to_date": "Използвате последната версия.", "check_failed": "Грешка при проверка за актуализации: {error}", "continue_anyway": "Продължаване с текущата версия...", "update_confirm": "Искате ли да актуализирате до последната версия? (Y/n)", "update_skipped": "Актуализацията беше пропусната.", "invalid_choice": "Невалиден избор. Моля, въведете 'Y' или 'n'.", "development_version": "Версия за разработка {current} > {latest}", "changelog_title": "Списък с промени", "rate_limit_exceeded": "Превишена граница на скоростта на API на GitHub. Проверка на актуализацията на пропускане."}, "totally_reset": {"title": "Пълно нулиране на Курсор", "checking_config": "Проверка на конфигурационния файл", "config_not_found": "Конфигурационният файл не беше намерен", "no_permission": "Конфигурационният файл не може да бъде прочетен или записан. Моля, проверете разрешенията на файла", "reading_config": "Четене на текущата конфигурация", "creating_backup": "Създаване на резервно копие на конфигурацията", "backup_exists": "Резервното копие вече съществува. Стъпката за резервно копие се пропуска", "generating_new_machine_id": "Генериране на ново машинно ID", "saving_new_config": "Запазване на новата конфигурация в JSON", "success": "Курсор беше успешно нулиран", "error": "Грешка при нулиране на Курсор: {error}", "press_enter": "Натисне<PERSON>е Enter, за да излезете", "reset_machine_id": "Нулиране на машинното ID", "database_connection_closed": "Връзката с базата данни беше затворена", "database_updated_successfully": "Базата данни беше успешно актуализирана", "connected_to_database": "Успешно свързване с базата данни", "updating_pair": "Актуализиране на двойката ключ-стойност", "db_not_found": "Файлът на базата данни не беше намерен: {path}", "db_permission_error": "Няма достъп до файла на базата данни. Моля, проверете разрешенията", "db_connection_error": "Грешка при свързване с базата данни: {error}", "feature_title": "ФУНКЦИИ", "feature_1": "Пълно премахване на настройките и конфигурациите на Курсор AI", "feature_2": "Изчистване на всички кеширани данни, включително историята и командите на AI", "feature_3": "Нулиране на машинното ID за заобикаляне на ограниченията за пробен период", "feature_4": "Генериране на нови случайни машинни идентификатори", "feature_5": "Премахване на персонализирани разширения и предпочитания", "feature_6": "Нулиране на информацията за пробния период и активиране", "feature_7": "Дълбоко сканиране за скрити файлове, свързани с лицензи и пробен период", "feature_8": "Безопасно запазване на файлове и приложения, които не са свързани с Курсор", "feature_9": "Съвместимост с Windows, macOS и Linux", "disclaimer_title": "ПРАВНО ИЗВЕСТВИЕ", "disclaimer_1": "Този инструмент ще изтрие за постоянно всички настройки на Курсор AI,", "disclaimer_2": "конфигурации и кеширани данни. Това действие е необратимо.", "disclaimer_3": "Вашите кодови файлове НЯМА да бъдат засегнати и този инструмент", "disclaimer_4": "е проектиран специално да се фокусира върху файловете на редактора на Курсор AI и механизмите за откриване на пробен период.", "disclaimer_5": "Другите приложения на вашата система няма да бъдат засегнати.", "disclaimer_6": "След като използвате този инструмент, ще трябва да преинсталирате Курсор AI.", "disclaimer_7": "Вие носите отговорността за използването му", "confirm_title": "Сигурни ли сте, че искате да продължите?", "confirm_1": "Това действие ще изтрие всички настройки на Курсор AI,", "confirm_2": "конфигурации и кеширани данни. Това действие е необратимо.", "confirm_3": "Вашите кодови файлове НЯМА да бъдат засегнати и този инструмент", "confirm_4": "е проектиран специално да се фокусира върху файловете на редактора на Курсор AI и механизмите за откриване на пробен период.", "confirm_5": "Другите приложения на вашата система няма да бъдат засегнати.", "confirm_6": "След като използвате този инструмент, ще трябва да преинсталирате Курсор AI.", "confirm_7": "Вие носите отговорността за използването му", "invalid_choice": "Моля, въведете 'Y' или 'n'", "skipped_for_safety": "Пропуснато за безопасност (не е свързано с Курсор): {path}", "deleted": "Изтрито: {path}", "error_deleting": "Грешка при изтриване на {path}: {error}", "not_found": "Файлът не беше намерен: {path}", "resetting_machine_id": "Нулиране на машинните идентификатори за заобикаляне на ограниченията за пробен период...", "created_machine_id": "Създадено е ново машинно ID: {path}", "error_creating_machine_id": "Грешка при създаване на файл за машинно ID {path}: {error}", "error_searching": "Грешка при търсене на файлове в {path}: {error}", "created_extended_trial_info": "Създадена е нова информация за удължен пробен период: {path}", "error_creating_trial_info": "Грешка при създаване на файл за информация за пробен период {path}: {error}", "resetting_cursor_ai_editor": "Нулиране на редактора на Курсор AI... Моля, изчакайте.", "reset_cancelled": "Нулирането беше отменено. Излизане без промени.", "windows_machine_id_modification_skipped": "Промяната на машинното ID на Windows беше пропусната: {error}", "linux_machine_id_modification_skipped": "Промяната на machine-id на Linux беше пропусната: {error}", "note_complete_machine_id_reset_may_require_running_as_administrator": "Забележка: Пълното нулиране на машинното ID може да изисква работа като администратор", "note_complete_system_machine_id_reset_may_require_sudo_privileges": "Забележка: Пълното нулиране на системното machine-id може да изисква sudo права", "windows_registry_instructions": "📝 ЗАБЕЛЕЖКА: За пълно нулиране на Windows може да се наложи да изчистите и записите в регистъра.", "windows_registry_instructions_2": "   Старти<PERSON><PERSON><PERSON><PERSON>е 'regedit' и потърсете и изтрийте ключове, съдържащи 'Cursor' или 'CursorAI' в HKEY_CURRENT_USER\\Software\\.\n", "reset_log_1": "Курсор AI беше напълно нулиран и ограниченията за пробен период бяха заобиколени!", "reset_log_2": "Моля, рестартирайте системата си, за да влязат в сила промените.", "reset_log_3": "Ще трябва да преинсталирате Курсор AI и сега трябва да имате нов пробен период.", "reset_log_4": "За най-добри резултати, помислете за следното:", "reset_log_5": "Използвайте различен имейл адрес при регистрация за нов пробен период", "reset_log_6": "Ако е възможно, използвайте VPN за промяна на IP адреса си", "reset_log_7": "Изчистете бисквитките и кеша на браузъра си, преди да посетите уебсайта на Курсор AI", "reset_log_8": "Ако проблемите продължават, опитайте да инсталирате Курсор AI на друго място", "reset_log_9": "Ако срещнете проблеми, посетете Github Issue Tracker и създайте проблем на https://github.com/yeongpin/cursor-free-vip/issues", "unexpected_error": "Възникна неочаквана грешка: {error}", "report_issue": "Моля, докладвайте този проблем в Github Issue Tracker на https://github.com/yeongpin/cursor-free-vip/issues", "keyboard_interrupt": "Процесът беше прекратен от потребителя. Излизане...", "return_to_main_menu": "Връщане към главното меню...", "process_interrupted": "Процесът беше прекратен. Излизане...", "press_enter_to_return_to_main_menu": "Натиснете Enter, за да се върнете към главното меню...", "removing_known": "Премахване на известни файлове за пробен период/лиценз", "performing_deep_scan": "Извършване на дълбоко сканиране за допълнителни файлове за пробен период/лиценз", "found_additional_potential_license_trial_files": "Намерени са {count} допълнителни потенциални файлове за лиценз/пробен период", "checking_for_electron_localstorage_files": "Проверка на Electron localStorage файлове", "no_additional_license_trial_files_found_in_deep_scan": "Няма намерени допълнителни файлове за лиценз/пробен период при дълбоко сканиране", "removing_electron_localstorage_files": "Премахване на Electron localStorage файлове", "electron_localstorage_files_removed": "Electron localStorage файлове бяха премахнати", "electron_localstorage_files_removal_error": "Грешка при премахване на Electron localStorage файлове: {error}", "removing_electron_localstorage_files_completed": "Премахването на Electron localStorage файлове беше завършено", "failed_to_reset_machine_guid": "Неуспешно нулиране на машината GUID", "delete_input_retry": "Изтриването на входа не е намерено, опит {опит}/{max_attempts}", "warning_6": "Ще трябва да настроите курсора AI отново, след като стартирате този инструмент.", "warning_5": "Други приложения във вашата система няма да бъдат засегнати.", "warning_4": "За да се насочите само към файловете на редактора на курсора и механизмите за откриване на проби.", "completed_in": "Завършено за {време} секунди", "removed": "Премахнато: {path}", "cursor_reset_completed": "Редакторът на Cursor AI е напълно нулиран и пробният откриване на проби!", "warning_1": "Това действие ще изтрие всички настройки на курсора AI,", "delete_input_error": "Грешка за намиране на вход за изтриване: {Грешка}", "warning_2": "конфигурации и кеширани данни. Това действие не може да бъде отменено.", "advanced_tab_retry": "Разш<PERSON><PERSON><PERSON>н раздел не е намерен, опит {опит}/{max_attempts}", "failed_to_delete_file": "Неуспешно изтриване на файл: {path}", "warning_3": "Вашите кодови файлове няма да бъдат засегнати и инструментът е проектиран", "delete_button_retry": "Бутонът за изтриване не е намерен, опит {опит}/{max_attempts}", "cursor_reset_cancelled": "Cursor AI Editor <PERSON>set отменено. Излизане, без да прави никакви промени.", "deep_scanning": "Извършване на дълбоко сканиране за допълнителни пробни/лицензионни файлове", "failed_to_delete_file_or_directory": "Неуспешно изтриване на файл или директория: {path}", "delete_button_not_found": "Бутонът за изтриване на акаунта не е намерен след множество опити", "delete_button_error": "Грешка за намиране на бутон за изтриване: {Грешка}", "already_on_settings": "Вече на страницата Настройки", "failed_to_delete_directory": "Неуспешно изтриване на директория: {path}", "found_danger_zone": "Намерено сечение на опасната зона", "cursor_reset_failed": "Ревелиране на Cursor AI Editor Неуспешно: {Грешка}", "navigating_to_settings": "Навигация до страницата на настройките ...", "warning_title": "Предупреждение", "resetting_cursor": "Нулиране на курсора AI редактор ... Моля, почакайте.", "login_redirect_failed": "Пренасочването на вход не успя, опитвайки се директна навигация ...", "advanced_tab_not_found": "Разширен раздел не е намерен след множество опити", "direct_advanced_navigation": "Опитване на директна навигация към раздел Advanced", "delete_button_clicked": "Щракнете върху бутона за изтриване на акаунта", "advanced_tab_error": "Грешка за намиране на разширен раздел: {Грешка}", "delete_input_not_found": "Изтриване на входа за потвърждение не е намерен след множество опити", "operation_cancelled": "Операция отменена. Излизане, без да прави никакви промени.", "failed_to_remove": "Неуспешно премахване: {path}", "warning_7": "Използвайте на свой риск", "delete_input_not_found_continuing": "Изтриването на входа за потвърждение не е намерено, опитвайки се да продължите така или иначе", "advanced_tab_clicked": "Щракна върху раздела Advanced"}, "chrome_profile": {"title": "Избор на Chrome Профил", "select_profile": "Изберете Chrome профил за използване:", "profile_list": "Налични профили:", "default_profile": "Профил по Подразбиране", "profile": "Профил {number}", "no_profiles": "Не са намерени Chrome профили", "error_loading": "Грешка при зареждане на Chrome профили: {error}", "profile_selected": "Избран профил: {profile}", "invalid_selection": "Невалиден избор. Моля, опитайте отново", "warning_chrome_close": "Предупреждение: Това ще затвори всички работещи Chrome процеси"}, "restore": {"title": "Възстановяване на машинен идентификатор от резервно копие", "starting": "Стартиране на процеса на възстановяване на машинния идентификатор", "no_backups_found": "Не са намерени резервни копия", "available_backups": "Налични резервни копия", "select_backup": "Изберете резервно копие за възстановяване", "to_cancel": "за отказ", "operation_cancelled": "Операцията е отменена", "invalid_selection": "Невалиден избор", "please_enter_number": "Моля, въведете валиден номер", "missing_id": "Липсващ идентификатор: {id}", "read_backup_failed": "Неуспешно четене на резервното копие: {error}", "current_file_not_found": "Текущият файл за съхранение не е намерен", "current_backup_created": "Създадено е резервно копие на текущия файл за съхранение", "storage_updated": "Файлът за съхранение е успешно актуализиран", "update_failed": "Неуспешно актуализиране на файла за съхранение: {error}", "sqlite_not_found": "SQLite базата данни не е намерена", "updating_sqlite": "Актуализиране на SQLite базата данни", "updating_pair": "Актуализиране на двойката ключ-стойност", "sqlite_updated": "SQLite базата данни е успешно актуализирана", "sqlite_update_failed": "Неуспешно актуализиране на SQLite базата данни: {error}", "machine_id_backup_created": "Създадено е резервно копие на файла с машинния идентификатор", "backup_creation_failed": "Неуспешно създаване на резервно копие: {error}", "machine_id_updated": "Файлът с машинния идентификатор е успешно актуализиран", "machine_id_update_failed": "Неуспешно актуализиране на файла с машинния идентификатор: {error}", "updating_system_ids": "Актуализиране на системните идентификатори", "system_ids_update_failed": "Неуспешно актуализиране на системните идентификатори: {error}", "permission_denied": "Достъпът е отказан. Опитайте да стартирате като администратор", "windows_machine_guid_updated": "Windows машинният GUID е успешно актуализиран", "update_windows_machine_guid_failed": "Неуспешно актуализиране на Windows машинния GUID: {error}", "windows_machine_id_updated": "Windows машинният идентификатор е успешно актуализиран", "update_windows_machine_id_failed": "Неуспешно актуализиране на Windows машинния идентификатор: {error}", "sqm_client_key_not_found": "Регистърни<PERSON>т ключ SQMClient не е намерен", "update_windows_system_ids_failed": "Неуспешно актуализиране на Windows системните идентификатори: {error}", "macos_platform_uuid_updated": "macOS платформеният UUID е успешно актуализиран", "failed_to_execute_plutil_command": "Неуспешно изпълнение на plutil командата", "update_macos_system_ids_failed": "Неуспешно актуализиране на macOS системните идентификатори: {error}", "ids_to_restore": "Машинни идентификатори за възстановяване", "confirm": "Сигурни ли сте, че искате да възстановите тези идентификатори?", "success": "Машинният идентификатор е успешно възстановен", "process_error": "Грешка при процеса на възстановяване: {error}", "press_enter": "Натиснете Enter, за да продължите"}, "config": {"configuration": "Конфигурация", "config_not_available": "Конфигурацията не е налична", "config_directory": "Конфигурационна директория", "disabled": "Деа<PERSON><PERSON>ив<PERSON><PERSON><PERSON><PERSON>", "using_temp_dir": "Използване на временна директория поради грешка: {path} (Грешка: {Грешка})", "file_permissions": "Разрешения за файлове: {разрешения}", "config_option_added": "Опция за конфигурация Добавена е: {опция}", "config_setup_error": "Грешка Настройване на конфигурация: {Грешка}", "config_dir_created": "Създадена директория Config: {path}", "config_force_update_disabled": "Конфигуриране на актуализацията на силата на файлове Деактивиране, пропускане на принудителна актуализация", "backup_created": "Създадено архивиране: {path}", "also_checked": "Също така проверен {path}", "force_update_failed": "Конфигурация за актуализация на силата не успя: {грешка}", "file_group": "Файлова група: {Group}", "config_created": "Config създаден: {config_file}", "config_force_update_enabled": "Конфигуриране на актуализацията на силата на файла, изпълняваща принудителна актуализация", "error_getting_file_stats": "Грешка Получаване на статистика на файловете: {Грешка}", "file_owner": "Собственик на файлове: {собственик}", "and": "И", "enabled": "А<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "error_checking_linux_paths": "Проверка на грешки в Linux пътища: {Грешка}", "the_file_might_be_corrupted_please_reinstall_cursor": "Файлът може да бъде покварен, моля, инсталирайте отново курсор", "please_make_sure_cursor_is_installed_and_has_been_run_at_least_once": "Моля, уверете се, че курсорът е инсталиран и е бил стартиран поне веднъж", "storage_file_is_valid_and_contains_data": "Файлът за съхранение е валиден и съдържа данни", "documents_path_not_found": "Документът не е намерен, използвайки текущата директория", "file_size": "Размер на файла: {размер} байта", "try_running": "Опитайте да стартирате: {команда}", "backup_failed": "Неуспешно архивиране на конфигурация: {грешка}", "neither_cursor_nor_cursor_directory_found": "Нито курсор, нито директория на курсора, намерени в {config_base}", "storage_file_is_empty": "Файлът за съхранение е празен: {Storage_path}", "storage_directory_not_found": "Директорията за съхранение не е намерена: {Storage_dir}", "storage_file_not_found": "Файлът за съхранение не е намерен: {Storage_path}", "error_reading_storage_file": "Грешка за четене на файл за съхранение: {Грешка}", "config_updated": "Config актуа<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>на", "config_removed": "Конфигурационният файл е премахнат за принудителна актуализация", "storage_file_found": "Намерен файл за съхранение: {Storage_path}", "permission_denied": "Разрешение е отказано: {storage_path}"}, "account_info": {"config_not_found": "Конфигурацията не е намерена.", "days_remaining": "Остават дни", "inactive": "Неактивен", "failed_to_get_subscription": "Не успя да получи информация за абонамента", "usage_not_found": "Използването не е намерено", "title": "Информация за акаунта", "days": "дни", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "premium": "Премия", "trial_remaining": "Останал професионален опит", "basic_usage": "Основно използване", "failed_to_get_token": "Не успя да получи знак", "pro": "Про", "email_not_found": "Имейл не е намерен", "lifetime_access_enabled": "Достъп до целия живот е активиран", "active": "Акти<PERSON><PERSON>н", "failed_to_get_account": "Не успя да получи информация за акаунта", "pro_trial": "Pro Trial", "subscription_type": "Тип абонамент", "token": "Токен", "usage": "Употреба", "token_not_found": "Токена не е намерен", "failed_to_get_email": "Не успя да получи имейл адрес", "enterprise": "Предприятие", "premium_usage": "Използване на премиум", "free": "Безплатно", "remaining_trial": "Останал опит", "failed_to_get_usage": "Не успя да получи информация за използване", "subscription_not_found": "Информацията за абонамента не е намерена", "subscription": "Абонамент", "team": "<PERSON><PERSON><PERSON><PERSON>", "failed_to_get_account_info": "Не успя да получи информация за акаунта"}, "account_delete": {"select_google_account": "Мо<PERSON><PERSON>, изберете вашия акаунт в Google ...", "advanced_tab_retry": "Разш<PERSON><PERSON><PERSON>н раздел не е намерен, опит {опит}/{max_attempts}", "advanced_tab_clicked": "Щракна върху раздела Advanced", "login_redirect_failed": "Пренасочването на вход не успя, опитвайки се директна навигация ...", "navigating_to_settings": "Навигация до страницата на настройките ...", "unexpected_error": "Неочаквана грешка: {грешка}", "starting_process": "Начален процес на изтриване на акаунта ...", "unexpected_page": "Неочаквана страница след влизане: {url}", "cancelled": "Изтриване на акаунт Анулира.", "success": "Ваши<PERSON>т Cursor акаунт е успешно изтрит!", "warning": "Предупреждение: Това ще изтрие постоянно вашия акаунт на курсор. Това действие не може да бъде отменено.", "delete_input_retry": "Изтриването на входа не е намерено, опит {опит}/{max_attempts}", "confirm_button_error": "Грешка за намиране на бутон за потвърждение: {Грешка}", "confirm_prompt": "Сигурни ли сте, че искате да продължите? (Y/N):", "trying_settings": "Опитвам се да се ориентирате към страницата за настройки ...", "advanced_tab_error": "Грешка за намиране на разширен раздел: {Грешка}", "found_danger_zone": "Намерено сечение на опасната зона", "waiting_for_auth": "В очакване на удостоверяване на Google ...", "logging_in": "Влезте в Google ...", "title": "Инструмент за изтриване на акаунта на Cursor Google", "login_successful": "Вход е успешен", "already_on_settings": "Вече на страницата Настройки", "direct_advanced_navigation": "Опитване на директна навигация към раздел Advanced", "account_deleted": "Сметката се изтрива успешно!", "advanced_tab_not_found": "Разширен раздел не е намерен след множество опити", "confirm_button_retry": "Бутон за потвърждение не е намерен, опит {опит}/{max_attempts}", "delete_button_clicked": "Щракнете върху бутона за изтриване на акаунта", "delete_input_not_found": "Изтриване на входа за потвърждение не е намерен след множество опити", "email_not_found": "Имейл не е намерен: {Грешка}", "found_email": "Намерен имейл: {имейл}", "failed": "Процесът на изтриване на акаунта не е успешен или е анулиран.", "delete_button_not_found": "Бутонът за изтриване на акаунта не е намерен след множество опити", "error": "Грешка по време на изтриване на акаунта: {Грешка}", "delete_button_error": "Грешка за намиране на бутон за изтриване: {Грешка}", "delete_button_retry": "Бутонът за изтриване не е намерен, опит {опит}/{max_attempts}", "auth_timeout": "Тайм -аут за удостоверяване, продължаващо така или иначе ...", "google_button_not_found": "Бутонът за влизане в Google не е намерен", "delete_input_error": "Грешка за намиране на вход за изтриване: {Грешка}", "interrupted": "Процесът на изтриване на акаунта, прекъснат от потребителя.", "confirm_button_not_found": "Бутон за потвърждение не е намерен след множество опити", "delete_input_not_found_continuing": "Изтриването на входа за потвърждение не е намерено, опитвайки се да продължите така или иначе", "typed_delete": "Въведено \"Изтриване\" в полето за потвърждение"}, "github_register": {"invalid_choice": "Невалиден избор. Моля, въведете „Да“ или „Не“", "warning2": "Изисква достъп до интернет и административни привилегии.", "check_browser_windows_for_manual_intervention_or_try_again_later": "Проверете прозорците на браузъра за ръчна намеса или опитайте отново по -късно.", "title": "GitHub + Cursor AI регистрация автоматизация", "warning1": "Този скрипт автоматизира създаването на акаунти, което може да наруши условията за обслужване на GitHub/Cursor.", "completed_successfully": "Регистрацията на GitHub + Cursor приключи успешно!", "credentials_saved": "Тези идентификационни данни са запазени в github_cursor_accounts.txt", "confirm": "Сигурни ли сте, че искате да продължите?", "email_address": "<PERSON><PERSON><PERSON><PERSON><PERSON> адрес", "github_password": "Парола за github", "cancelled": "Операция отменена", "warnings_header": "Предупреждения", "github_username": "Потребителско име на GitHub", "starting_automation": "Стартиране на автоматизация ...", "feature1": "Генерира временен имейл с помощта на 1SecMail.", "feature3": "Проверява автоматично имейла на GitHub.", "feature2": "Регистрира нов акаунт в GitHub със случайни идентификационни данни.", "program_terminated": "Програма, прекратена от потребителя", "features_header": "Характеристики", "registration_encountered_issues": "Регистрацията на GitHub + курсор срещна проблеми.", "feature5": "Възстановява идентификатора на машината, за да заобиколи пробното откриване.", "warning3": "Captcha или допълнителна проверка може да прекъсне автоматизацията.", "feature6": "Запазва всички идентификационни данни във файл.", "feature4": "Влиза в курсора AI, използвайки автентификация на GitHub.", "warning4": "Използвайте отговорно и на свой риск."}, "oauth": {"could_not_check_usage_count": "Не можах да проверя броя на употребата: {Грешка}", "try_running_without_sudo_admin": "Опитайте да стартирате без привилегии за SUDO/администратор", "browser_setup_completed": "Настройката на браузъра приключи успешно", "status_check_error": "Грешка в проверката на състоянието: {Грешка}", "warning_could_not_kill_existing_browser_processes": "Предупреждение: Не можа да убие съществуващите процеси на браузъра: {Грешка}", "failed_to_extract_auth_info": "Неуспешно извличане на Auth Info: {Грешка}", "browser_setup_failed": "Настройката на браузъра не бе успешна: {грешка}", "detected_platform": "Открита платформа: {платформа}", "auth_update_success": "AUTH актуализиране на успеха", "account_is_still_valid": "Акаунт все още е валиден (Използване: {usage})", "running_as_root_warning": "Изпълнението като root не се препоръчва за автоматизация на браузъра", "using_browser_profile": "Използване на профил на браузъра: {профил}", "warning_browser_close": "ПРЕДУПРЕЖДЕНИЕ: Това ще затвори всички работещи {браузър} процеси", "google_start": "Google Start", "failed_to_delete_account": "Неуспешно изтриване на акаунт: {Грешка}", "initializing_browser_setup": "Инициализиране на настройка на браузъра ...", "consider_running_without_sudo": "Помислете за стартиране на скрипта без sudo", "found_browser_user_data_dir": "Намерено {браузър} директория на потребителски данни: {path}", "using_first_available_chrome_profile": "Използване на първи наличен хромиран профил: {профил}", "invalid_authentication_type": "Невалиден тип удостоверяване", "could_not_find_email": "Не можах да намеря имейл: {грешка}", "found_default_chrome_profile": "Намерен хромиран профил по подразбиране", "user_data_dir_not_found": "{Браузър} Директория за потребителски данни не е намерена в {path}, вместо това ще опитате Chrome", "using_configured_browser_path": "Използване на конфигуриран {браузър} път: {path}", "could_not_find_usage_count": "Не можах да намеря броя на употребата: {грешка}", "waiting_for_authentication": "В очакване на удостоверяване ...", "browser_not_found_trying_chrome": "Не можах да намеря {браузър}, да опитаме хром вместо това", "found_cookies": "Намерени {count} бисквитки", "no_chrome_profiles_found": "Не са намерени хромирани профили, използвайки по подразбиране", "authentication_button_not_found": "Бутонът за удостоверяване не е намерен", "starting_google_authentication": "Започване на автентификация на Google ...", "starting_re_authentication_process": "Стартиране на процес на повторна устойчивост ...", "authentication_successful": "Успехът на удостоверяването - имейл: {имейл}", "failed_to_delete_expired_account": "Неуспешно изтриване на изтекъл акаунт", "authentication_failed": "Удостоверяването не бе успешно: {Грешка}", "error_getting_user_data_directory": "Грешка Получаване на директория за потребителски данни: {Грешка}", "found_email": "Намерен имейл: {имейл}", "token_extraction_error": "Грешка в извличането на токени: {Грешка}", "account_has_reached_maximum_usage": "Акаунт е достигнал максимално използване, {изтриване}", "found_chrome_at": "Наме<PERSON>ен хром на: {path}", "starting_new_authentication_process": "Стартиране на нов процес на удостоверяване ...", "github_start": "<PERSON><PERSON><PERSON> start", "error_finding_chrome_profile": "Грешка за намиране на хромиран профил, използвайки по подразбиране: {грешка}", "navigating_to_authentication_page": "Навигация към страницата за удостоверяване ...", "failed_to_delete_account_or_re_authenticate": "Неуспешно изтриване на акаунт или повторно автентика: {Грешка}", "starting_browser": "Старти<PERSON><PERSON><PERSON> браузър на: {path}", "no_compatible_browser_found": "Не е намерен съвместим браузър. Моля, инсталирайте Google Chrome или Chromium.", "killing_browser_processes": "Убиване {браузър} процеси ...", "browser_failed": "Браузърът не успя да започне: {грешка}", "browser_closed": "Браузърът е затворен", "starting_new_google_authentication": "Стартиране на ново удостоверяване на Google ...", "usage_count": "Брой на употребата: {Използване}", "authentication_timeout": "Време за изчакване на удостоверяване", "found_browser_data_directory": "Намерена директория за данни на браузъра: {path}", "already_on_settings_page": "Вече на страницата Настройки!", "supported_browsers": "Поддържани браузъри за {платформа}", "page_changed_checking_auth": "Страницата се промени, проверка на Auth ...", "browser_failed_to_start": "Браузърът не успя да започне: {грешка}", "starting_github_authentication": "Стартиране на автентификация на GitHub ...", "auth_update_failed": "Auth Update не успя", "missing_authentication_data": "Липсващи данни за удостоверяване: {data}", "please_select_your_google_account_to_continue": "Моля, изберете вашия акаунт в Google, за да продължите ...", "authentication_successful_getting_account_info": "Удостоверяване е успешно, получаване на информация за акаунта ...", "redirecting_to_authenticator_cursor_sh": "Пренасочване към Authenticator.Cursor.sh ...", "profile_selection_error": "Грешка по време на избор на профил: {Грешка}", "browser_failed_to_start_fallback": "Браузърът не успя да започне: {грешка}"}, "auth_check": {"connection_error": "Грешка в връзката", "operation_cancelled": "Операция, анулирана от потребителя", "error_generating_checksum": "Грешка Генериране на контролна сума: {Грешка}", "user_unauthorized": "Потребителят е неоторизиран", "authorization_failed": "Упълномощаването не успя!", "error_getting_token_from_db": "Грешка Получаване на знака от база данни: {Грешка}", "checking_usage_information": "Проверка на информацията за използването ...", "token_found_in_db": "То<PERSON><PERSON><PERSON>, намерен в базата данни", "unexpected_status_code": "Неочакван код на състоянието: {код}", "token_source": "Вземете знак от база данни или въведете ръчно? (D/M, по подразбиране: D)", "usage_response_status": "Състояние на отговора на употребата: {Отговор}", "enter_token": "Въведете вашия токен на курсора:", "getting_token_from_db": "Получаване на знака от база данни ...", "invalid_token": "Невалиден маркер", "token_length": "Дължина на токена: {дължина} символи", "authorization_successful": "Разрешение успешно!", "unexpected_error": "Неочаквана грешка: {грешка}", "user_authorized": "Потребителят е упълномощен", "cursor_acc_info_not_found": "cursor_acc_info.py не е намерен", "check_error": "Разрешение за проверка на грешки: {Грешка}", "usage_response": "Отговор на употреба: {Отговор}", "check_usage_response": "Проверете отговора на употребата: {Отговор}", "checking_authorization": "Проверка на разрешението ...", "request_timeout": "Заявка изтече", "jwt_token_warning": "Изглежда, че Token е във формат JWT, но API Check върна неочакван код на състоянието. Маркерът може да е валиден, но достъпът до API е ограничен.", "token_not_found_in_db": "Токена не е намерен в базата данни"}, "token": {"unexpected_error": "Неочаквана грешка по време на опресняване на маркера: {грешка}", "request_timeout": "Заявка за опресняване на сървъра се изчерпа", "no_access_token": "Няма маркер за достъп в отговор", "extraction_error": "Грешка при извличане на токен: {Грешка}", "refresh_failed": "Опресняване на токена не успя: {грешка}", "refreshing": "Освежаващ жетон ...", "refresh_success": "Токенът се освежи! Валидно за {дни} дни (изтича: {изтича})", "invalid_response": "Нева<PERSON><PERSON><PERSON><PERSON><PERSON> json отговор от опреснен сървър", "server_error": "Опреснителна грешка в сървъра: http {status}", "connection_error": "Грешка в връзката за опресняване на сървъра"}, "bypass": {"menu_option": "Проверка на версията на байпас курсор", "backup_created": "Създадено архивиране: {path}", "write_failed": "Неуспешно напишете product.json: {грешка}", "description": "Този инструмент променя продукта на Cursor.json, за да заобиколи ограниченията на версията", "found_product_json": "Намерен product.json: {path}", "current_version": "Текуща версия: {версия}", "version_updated": "Версията се актуализира от {old} до {new}", "title": "Байпасен инструмент за версия на курсора", "file_not_found": "Файлът не е намерен: {path}", "no_write_permission": "Без разрешение за запис за файл: {path}", "stack_trace": "Сметка на стек", "product_json_not_found": "product.json не е намерен в обикновените Linux пътеки", "no_update_needed": "Не е необходима актуализация. Текущата версия {версия} вече е> = 0,46.0", "starting": "Стартиране на байпас на версията на курсора ...", "unsupported_os": "Неподдържана операционна система: {Система}", "read_failed": "Неуспешно четене на product.json: {грешка}", "localappdata_not_found": "LocalAppData Environment Променлива не е намерена", "bypass_failed": "Байпас на версията не бе успешен: {Грешка}"}, "bypass_token_limit": {"title": "Байпасен инструмент за ограничаване на маркера", "press_enter": "Натиснете Enter, за да продължите ...", "description": "Този инструмент променя файла workbench.desktop.main.js, за да заобиколи ограничението на маркера"}, "browser_profile": {"profile": "Профил {номер}", "no_profiles": "Не {браузър} профили, намерени", "error_loading": "Грешка за зареждане {браузър} профили: {грешка}", "profile_list": "Налични {браузър} профили:", "invalid_selection": "Невалидна селекция. Моля, опитайте отново.", "profile_selected": "Избран профил: {профил}", "select_profile": "Изберете {Browser} профил, който да използвате:", "title": "Избор на профил на браузъра", "default_profile": "Профил по подразбиране"}, "manual_auth": {"proceed_prompt": "Продължете? (Y/N):", "auth_updated_successfully": "Информацията за удостоверяване се актуализира успешно!", "token_verification_skipped": "Проверката на токена пропусна (check_user_authorized.py не е намерен)", "auth_type_selected": "Избран тип удостоверяване: {type}", "auth_type_google": "Google", "token_required": "Необходим е жетон", "continue_anyway": "Продължете така или иначе? (Y/N):", "auth_type_github": "<PERSON><PERSON><PERSON>", "verifying_token": "Проверка на валидността на жетони ...", "random_email_generated": "Генериран произволен имейл: {имейл}", "auth_type_prompt": "Изберете Тип удостоверяване:", "error": "Грешка: {Грешка}", "operation_cancelled": "Операция отменена", "auth_type_auth0": "Auth_0 (по подразбиране)", "token_verification_error": "Грешка в проверката на токена: {грешка}", "email_prompt": "Въведете имейл (оставете празно за случаен имейл):", "token_verified": "Токен успешно се проверява!", "token_prompt": "Въведете вашия токен на курсора (access_token/refresh_token):", "invalid_token": "Невалиден маркер. Удостоверяване абортирано.", "confirm_prompt": "Моля, потвърдете следната информация:", "auth_update_failed": "Неуспешно актуализиране на информацията за удостоверяване", "title": "Ръчно удостоверяване на курсора", "updating_database": "Актуализиране на базата данни за удостоверяване на курсора ..."}, "tempmail": {"no_email": "Не е намерен имейл за проверка на курсора", "config_error": "Грешка в конфигурацията на файла: {грешка}", "general_error": "Възникна грешка: {грешка}", "configured_email": "Конфигу<PERSON>и<PERSON>ан имейл: {имейл}", "extract_code_failed": "Кодът за проверка на екстракт не успя: {Грешка}", "checking_email": "Проверка за имейл за проверка на курсора ...", "no_code": "Не можа да получи код за проверка", "email_found": "Намерен имейл за проверка на курсора", "check_email_failed": "Проверете имейла не е успешен: {Грешка}", "verification_code": "Код за проверка: {код}"}}